# Chrome扩展侧边栏重构开发计划

## 📋 项目概述

将现有的Chrome扩展从弹窗(popup)模式重构为侧边栏(side panel)模式，提升用户体验和功能可用性。

### 🎯 重构目标
- ✅ 保持所有现有功能完整性
- ✅ 提升用户交互体验
- ✅ 增强界面稳定性和可用性
- ✅ 保持与MDAC网站的完美兼容

### 📊 技术要求
- **Chrome版本要求**: Chrome 114+ (Side Panel API支持)
- **API兼容性**: Chrome Side Panel API
- **功能保持**: 双输入源、AI解析、图片处理、表单填充
- **通信机制**: 保持content script通信不变

## 🔧 技术架构分析

### 当前架构
```
popup.html (弹窗界面)
├── popup.css (弹窗样式)
├── popup.js (主要逻辑)
├── background-classic.js (后台服务)
├── content-script.js (内容脚本)
└── 各种管理器模块
```

### 目标架构
```
sidepanel.html (侧边栏界面)
├── sidepanel.css (侧边栏样式)
├── sidepanel.js (主要逻辑)
├── background-classic.js (后台服务 - 需更新)
├── content-script.js (内容脚本 - 保持不变)
└── 各种管理器模块 (保持不变)
```

## 📝 详细实施计划

### 阶段1: 准备和配置 (预计1-2小时)

#### 1.1 代码备份
- [ ] 创建当前代码的完整备份
- [ ] 建立版本控制分支 `feature/sidepanel-refactor`
- [ ] 记录当前功能状态基线

#### 1.2 Manifest.json更新
**文件**: `manifest.json`

**修改内容**:
```json
{
  "manifest_version": 3,
  "name": "MDAC AI智能分析工具",
  "version": "2.0.0",
  "description": "基于Gemini AI的智能内容解析、数据验证和地址翻译工具 - 侧边栏版本",
  
  "permissions": [
    "sidePanel",
    "activeTab",
    "storage",
    "scripting",
    "tabs"
  ],
  
  "host_permissions": [
    "https://imigresen-online.imi.gov.my/*",
    "https://generativelanguage.googleapis.com/*"
  ],
  
  "background": {
    "service_worker": "background-classic.js"
  },
  
  "side_panel": {
    "default_path": "sidepanel.html"
  },
  
  "action": {
    "default_title": "打开MDAC AI智能助手侧边栏",
    "default_icon": {
      "16": "icons/icon16.png",
      "32": "icons/icon32.png",
      "48": "icons/icon48.png",
      "128": "icons/icon128.png"
    }
  },
  
  "content_scripts": [
    {
      "matches": ["https://imigresen-online.imi.gov.my/*"],
      "js": ["form-field-detector.js", "data-preview-manager.js", "error-recovery-manager.js", "ai-config.js", "content-script.js"],
      "css": ["content-styles.css"],
      "run_at": "document_end"
    }
  ],
  
  "icons": {
    "16": "icons/icon16.png",
    "32": "icons/icon32.png",
    "48": "icons/icon48.png",
    "128": "icons/icon128.png"
  },

  "options_page": "options.html",
  
  "commands": {
    "open-side-panel": {
      "suggested_key": {
        "default": "Ctrl+Shift+S",
        "mac": "Command+Shift+S"
      },
      "description": "打开MDAC AI侧边栏"
    }
  },
  
  "content_security_policy": {
    "extension_pages": "script-src 'self'; object-src 'self'; connect-src 'self' https://generativelanguage.googleapis.com https://imigresen-online.imi.gov.my;"
  }
}
```

**关键变更**:
- ✅ 添加 `"sidePanel"` 权限
- ✅ 添加 `side_panel` 配置项
- ✅ 更新 `action` 配置
- ✅ 添加侧边栏快捷键
- ✅ 版本号升级到2.0.0

### 阶段2: 界面重构 (预计2-3小时)

#### 2.1 HTML文件重构
**操作**: 复制 `popup.html` → `sidepanel.html`

**修改要点**:
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MDAC AI智能助手 - 侧边栏</title>
    <link rel="stylesheet" href="sidepanel.css">
</head>
<body class="sidepanel-body">
    <!-- 保持现有的HTML结构，但添加侧边栏特定的class -->
    <div class="sidepanel-container">
        <!-- 现有内容 -->
    </div>
    
    <!-- 脚本引用更新 -->
    <script src="data-preview-manager.js"></script>
    <script src="error-recovery-manager.js"></script>
    <script src="fill-monitor.js"></script>
    <script src="sidepanel.js"></script>
</body>
</html>
```

#### 2.2 CSS样式重构
**文件**: 复制 `popup.css` → `sidepanel.css`

**关键布局调整**:
```css
/* 侧边栏特定样式 */
.sidepanel-body {
    width: 100%;
    min-height: 100vh;
    margin: 0;
    padding: 0;
    background: #f8f9fa;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.sidepanel-container {
    width: 100%;
    max-width: none;
    min-height: 100vh;
    padding: 16px;
    box-sizing: border-box;
}

/* 响应式布局优化 */
@media (max-width: 400px) {
    .sidepanel-container {
        padding: 12px;
    }
    
    .input-section {
        margin-bottom: 12px;
    }
}

/* 双输入源区域优化 */
.input-section {
    margin-bottom: 16px;
    border-radius: 8px;
    overflow: hidden;
}

.supplement-input-section {
    background: #f0f8ff;
    border-radius: 8px;
    margin-bottom: 16px;
    border: 1px solid #b3d9ff;
}

/* 按钮组优化 */
.button-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 12px;
}

.btn {
    width: 100%;
    padding: 10px 16px;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.2s ease;
}

/* 状态显示优化 */
.status-container {
    position: sticky;
    top: 0;
    background: white;
    z-index: 100;
    padding: 8px 0;
    border-bottom: 1px solid #e1e5e9;
    margin-bottom: 16px;
}
```

**布局优化要点**:
- ✅ 宽度自适应侧边栏容器
- ✅ 高度适配浏览器窗口
- ✅ 优化双输入源区域布局
- ✅ 改进按钮和控件排列
- ✅ 增强滚动体验

### 阶段3: 功能迁移 (预计2-3小时)

#### 3.1 JavaScript逻辑迁移
**文件**: 复制 `popup.js` → `sidepanel.js`

**关键修改**:
```javascript
/**
 * MDAC AI智能填充工具 - 侧边栏版本
 * 从弹窗模式迁移到侧边栏模式
 */
class MDACAssistantSidePanel {
    constructor() {
        // 保持原有的所有属性和方法
        this.aiStatus = 'ready';
        this.parsedData = null;
        this.supplementData = null;
        // ... 其他属性保持不变
        
        // 侧边栏特定的初始化
        this.initializeSidePanel();
    }

    /**
     * 侧边栏特定初始化
     */
    async initializeSidePanel() {
        console.log('🚀 MDAC AI侧边栏正在初始化...');
        
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.init());
        } else {
            await this.init();
        }
        
        // 侧边栏特定的事件监听
        this.setupSidePanelEvents();
    }

    /**
     * 设置侧边栏特定事件
     */
    setupSidePanelEvents() {
        // 监听窗口大小变化
        window.addEventListener('resize', () => this.handleResize());
        
        // 监听页面可见性变化
        document.addEventListener('visibilitychange', () => this.handleVisibilityChange());
        
        // 监听侧边栏关闭事件
        window.addEventListener('beforeunload', () => this.handleSidePanelClose());
    }

    /**
     * 处理窗口大小变化
     */
    handleResize() {
        // 调整布局以适应新的窗口大小
        this.adjustLayout();
    }

    /**
     * 处理可见性变化
     */
    handleVisibilityChange() {
        if (document.hidden) {
            console.log('📱 侧边栏已隐藏');
        } else {
            console.log('📱 侧边栏已显示');
            // 重新检查与页面的连接状态
            this.checkPageConnection();
        }
    }

    /**
     * 处理侧边栏关闭
     */
    handleSidePanelClose() {
        console.log('📱 侧边栏正在关闭');
        // 保存当前状态
        this.saveCurrentState();
    }

    /**
     * 调整布局
     */
    adjustLayout() {
        const container = document.querySelector('.sidepanel-container');
        if (container) {
            const width = window.innerWidth;
            if (width < 350) {
                container.classList.add('compact-mode');
            } else {
                container.classList.remove('compact-mode');
            }
        }
    }

    /**
     * 检查与页面的连接状态
     */
    async checkPageConnection() {
        try {
            const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
            const activeTab = tabs[0];
            
            if (activeTab && activeTab.url.includes('imigresen-online.imi.gov.my')) {
                this.updateConnectionStatus('connected');
            } else {
                this.updateConnectionStatus('disconnected');
            }
        } catch (error) {
            console.error('检查页面连接失败:', error);
            this.updateConnectionStatus('error');
        }
    }

    /**
     * 更新连接状态显示
     */
    updateConnectionStatus(status) {
        const statusElement = document.querySelector('.connection-status');
        if (statusElement) {
            statusElement.className = `connection-status ${status}`;
            statusElement.textContent = this.getConnectionStatusText(status);
        }
    }

    /**
     * 获取连接状态文本
     */
    getConnectionStatusText(status) {
        const statusTexts = {
            connected: '🟢 已连接到MDAC网站',
            disconnected: '🟡 请打开MDAC网站',
            error: '🔴 连接异常'
        };
        return statusTexts[status] || '🟡 状态未知';
    }

    // 保持所有原有的方法不变
    // ... 其他方法保持与popup.js完全一致
}

// 初始化侧边栏
const mdacSidePanel = new MDACAssistantSidePanel();
```

#### 3.2 后台脚本更新
**文件**: `background-classic.js`

**新增功能**:
```javascript
// 在现有的 MDACBackground 类中添加侧边栏支持

/**
 * 处理侧边栏相关操作
 */
MDACBackground.prototype.handleSidePanelAction = function(action, tabId, callback) {
    switch (action) {
        case 'open':
            chrome.sidePanel.open({ tabId: tabId }, callback);
            break;
        case 'close':
            // Chrome Side Panel API 目前不支持程序化关闭
            console.log('侧边栏关闭需要用户手动操作');
            break;
        default:
            callback({ success: false, error: '未知的侧边栏操作' });
    }
};

// 更新消息处理器
MDACBackground.prototype.handleMessage = function(message, sender, sendResponse) {
    var self = this;
    
    try {
        switch (message.action) {
            // ... 保持现有的所有case

            case 'open-side-panel':
                chrome.tabs.query({ active: true, currentWindow: true }, function(tabs) {
                    if (tabs[0]) {
                        chrome.sidePanel.open({ tabId: tabs[0].id }, function() {
                            sendResponse({ success: true });
                        });
                    } else {
                        sendResponse({ success: false, error: '无法获取当前标签页' });
                    }
                });
                break;

            case 'check-side-panel-support':
                sendResponse({ 
                    success: true, 
                    supported: typeof chrome.sidePanel !== 'undefined' 
                });
                break;

            default:
                sendResponse({ success: false, error: '未知操作' });
        }
    } catch (error) {
        console.error('处理消息失败:', error);
        sendResponse({ success: false, error: error.message });
    }
};

// 添加扩展图标点击处理
chrome.action.onClicked.addListener(function(tab) {
    // 打开侧边栏
    chrome.sidePanel.open({ tabId: tab.id });
});
```

### 阶段4: 测试和优化 (预计1-2小时)

#### 4.1 功能完整性测试清单
- [ ] **基础功能测试**
  - [ ] 侧边栏正常打开和显示
  - [ ] 扩展图标点击响应
  - [ ] 界面布局正确显示
  - [ ] 滚动功能正常

- [ ] **双输入源功能测试**
  - [ ] 第一输入框（AI解析）正常工作
  - [ ] 第二输入框（补充信息）正常工作
  - [ ] 数据持久化保存功能
  - [ ] 数据合并预览功能

- [ ] **AI功能测试**
  - [ ] AI连接测试正常
  - [ ] 内容解析功能正常
  - [ ] 图片文字提取功能正常
  - [ ] 错误处理机制正常

- [ ] **表单填充测试**
  - [ ] 与MDAC网站通信正常
  - [ ] 表单数据填充正确
  - [ ] 填充状态反馈正常

#### 4.2 用户体验测试清单
- [ ] **界面响应性**
  - [ ] 侧边栏打开速度 < 1秒
  - [ ] 界面操作响应及时
  - [ ] 状态更新实时显示
  - [ ] 错误提示清晰明确

- [ ] **交互体验**
  - [ ] 侧边栏可以固定显示
  - [ ] 不会因点击页面而关闭
  - [ ] 与页面内容不冲突
  - [ ] 快捷键功能正常

#### 4.3 兼容性测试清单
- [ ] **Chrome版本兼容性**
  - [ ] Chrome 114+ 正常工作
  - [ ] 旧版本Chrome优雅降级
  - [ ] API兼容性检查

- [ ] **屏幕适配测试**
  - [ ] 不同分辨率下正常显示
  - [ ] 侧边栏宽度自适应
  - [ ] 移动设备兼容性

### 阶段5: 文档更新和发布 (预计30分钟)

#### 5.1 文档更新
- [ ] 更新 README.md
- [ ] 更新用户使用指南
- [ ] 创建版本变更日志
- [ ] 更新开发文档

#### 5.2 发布准备
- [ ] 版本号更新验证
- [ ] 打包文件检查
- [ ] 发布说明准备

## ⚠️ 风险控制和应急方案

### 技术风险
1. **Chrome版本兼容性风险**
   - **风险**: Chrome 114以下版本不支持Side Panel API
   - **解决方案**: 添加版本检测和优雅降级机制

2. **布局适配风险**
   - **风险**: 侧边栏布局可能在某些分辨率下显示异常
   - **解决方案**: 充分的响应式测试和CSS媒体查询

3. **功能迁移风险**
   - **风险**: 某些popup特有功能可能在侧边栏中不工作
   - **解决方案**: 分阶段测试，保留原popup文件作为备份

### 应急回滚方案
1. **快速回滚**: 恢复原manifest.json和popup文件
2. **部分回滚**: 保留侧边栏功能，同时支持popup模式
3. **渐进式发布**: 先发布beta版本进行用户测试

## 📊 项目时间线

| 阶段 | 预计时间 | 关键里程碑 |
|------|----------|------------|
| 阶段1: 准备配置 | 1-2小时 | manifest.json更新完成 |
| 阶段2: 界面重构 | 2-3小时 | 侧边栏界面显示正常 |
| 阶段3: 功能迁移 | 2-3小时 | 所有功能正常工作 |
| 阶段4: 测试优化 | 1-2小时 | 通过所有测试用例 |
| 阶段5: 文档发布 | 30分钟 | 完成文档和发布 |
| **总计** | **6-10小时** | **侧边栏版本发布** |

## 🎯 成功标准

### 功能标准
- ✅ 所有现有功能100%保持
- ✅ 侧边栏界面正常显示和交互
- ✅ 与MDAC网站完美兼容
- ✅ 性能不低于原popup版本

### 用户体验标准
- ✅ 侧边栏打开速度 < 1秒
- ✅ 界面操作响应时间 < 200ms
- ✅ 用户学习成本最小化
- ✅ 错误处理用户友好

### 技术标准
- ✅ Chrome 114+ 完全兼容
- ✅ 代码质量保持高标准
- ✅ 无JavaScript错误
- ✅ 内存使用优化

这个重构计划将确保Chrome扩展成功从弹窗模式转换为侧边栏模式，同时保持所有现有功能的完整性和用户体验的优化。
