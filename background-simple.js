/**
 * MDAC AI智能填充工具 - 后台服务脚本 (简化版)
 * 处理扩展的后台逻辑、消息传递和API调用
 */

// 简化的配置对象，防止模块加载失败
const DEFAULT_CONFIG = {
    GEMINI_API_KEY: 'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s',
    GEMINI_MODEL: 'gemini-2.5-flash-lite-preview-06-17',
    API_BASE_URL: 'https://generativelanguage.googleapis.com/v1beta/models'
};

class MDACBackground {
    constructor() {
        this.aiCache = new Map();
        this.config = DEFAULT_CONFIG;
        this.init();
    }

    async init() {
        try {
            // 尝试加载完整配置
            const module = await import('./ai-config.js');
            if (module.GEMINI_CONFIG) {
                this.config = { ...this.config, ...module.GEMINI_CONFIG };
            }
            console.log('AI配置加载成功');
        } catch (error) {
            console.warn('使用默认AI配置:', error);
        }

        this.setupEventListeners();
        await this.initializeStorage();
        console.log('MDAC AI扩展后台服务已启动');
    }

    setupEventListeners() {
        // 扩展安装/更新事件
        chrome.runtime.onInstalled.addListener((details) => {
            this.handleInstalled(details);
        });

        // 消息传递
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true;
        });

        // 快捷键命令
        chrome.commands.onCommand.addListener((command) => {
            this.handleCommand(command);
        });
    }

    async initializeStorage() {
        try {
            const storage = await chrome.storage.sync.get(['mdacSettings']);
            if (!storage.mdacSettings) {
                await chrome.storage.sync.set({
                    mdacSettings: {
                        aiEnabled: true,
                        geminiApiKey: this.config.GEMINI_API_KEY || DEFAULT_CONFIG.GEMINI_API_KEY,
                        aiModel: this.config.GEMINI_MODEL || DEFAULT_CONFIG.GEMINI_MODEL,
                        aiTemperature: 0.2,
                        debugMode: false
                    }
                });
                console.log('已初始化AI默认设置');
            }
        } catch (error) {
            console.error('初始化AI设置失败:', error);
        }
    }

    async handleInstalled(details) {
        if (details.reason === 'install') {
            console.log('MDAC AI扩展首次安装');
            try {
                await chrome.tabs.create({
                    url: chrome.runtime.getURL('options.html')
                });
            } catch (error) {
                console.error('打开设置页面失败:', error);
            }
        } else if (details.reason === 'update') {
            console.log('MDAC AI扩展已更新到版本:', chrome.runtime.getManifest().version);
        }
    }

    async handleMessage(message, sender, sendResponse) {
        try {
            switch (message.action) {
                case 'test-ai-connection':
                    const testResult = await this.testAIConnection();
                    sendResponse({ success: true, result: testResult });
                    break;

                case 'get-ai-response':
                    const aiResponse = await this.getAIResponse(message.prompt, message.context);
                    sendResponse({ success: true, result: aiResponse });
                    break;

                case 'analyze-content':
                    const analysis = await this.analyzeContent(message.content, message.type);
                    sendResponse({ success: true, result: analysis });
                    break;

                default:
                    sendResponse({ success: false, error: '未知操作' });
            }
        } catch (error) {
            console.error('处理消息失败:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    handleCommand(command) {
        if (command === 'fill-form') {
            this.triggerFormFill();
        }
    }

    async triggerFormFill() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            if (tab && tab.url.includes('imigresen-online.imi.gov.my')) {
                await chrome.tabs.sendMessage(tab.id, { action: 'start-ai-fill' });
            }
        } catch (error) {
            console.error('触发表单填充失败:', error);
        }
    }

    async testAIConnection() {
        try {
            const settings = await chrome.storage.sync.get(['mdacSettings']);
            const apiKey = settings.mdacSettings?.geminiApiKey || this.config.GEMINI_API_KEY;
            
            const response = await fetch(`${this.config.API_BASE_URL}/${this.config.GEMINI_MODEL}:generateContent?key=${apiKey}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    contents: [{
                        parts: [{ text: '请回复"连接正常"' }]
                    }],
                    generationConfig: {
                        temperature: 0,
                        maxOutputTokens: 50
                    }
                })
            });

            if (response.ok) {
                const data = await response.json();
                return {
                    connected: true,
                    response: data.candidates?.[0]?.content?.parts?.[0]?.text || '连接成功'
                };
            } else {
                throw new Error(`API请求失败: ${response.status}`);
            }
        } catch (error) {
            console.error('AI连接测试失败:', error);
            return { connected: false, error: error.message };
        }
    }

    async getAIResponse(prompt, context = '') {
        try {
            const settings = await chrome.storage.sync.get(['mdacSettings']);
            const apiKey = settings.mdacSettings?.geminiApiKey || this.config.GEMINI_API_KEY;
            
            const fullPrompt = context ? `${context}\n\n${prompt}` : prompt;
            
            const response = await fetch(`${this.config.API_BASE_URL}/${this.config.GEMINI_MODEL}:generateContent?key=${apiKey}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    contents: [{
                        parts: [{ text: fullPrompt }]
                    }],
                    generationConfig: {
                        temperature: 0.2,
                        maxOutputTokens: 1024
                    }
                })
            });

            if (response.ok) {
                const data = await response.json();
                return data.candidates?.[0]?.content?.parts?.[0]?.text || '';
            } else {
                throw new Error(`API请求失败: ${response.status}`);
            }
        } catch (error) {
            console.error('获取AI响应失败:', error);
            throw error;
        }
    }

    async analyzeContent(content, type = 'general') {
        const prompts = {
            general: `请分析以下内容并提取相关信息：\n\n${content}`,
            form: `请从以下内容中提取适合MDAC表单的信息：\n\n${content}`,
            address: `请将以下地址翻译为英文：\n\n${content}`
        };

        return await this.getAIResponse(prompts[type] || prompts.general);
    }
}

// 初始化后台服务
const mdacBackground = new MDACBackground();

// 导出以供测试使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MDACBackground;
}
