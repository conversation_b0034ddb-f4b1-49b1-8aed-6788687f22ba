/**
 * MDAC AI智能填充工具 - 后台服务脚本
 * 处理扩展的后台逻辑、消息传递和API调用
 */

// 导入AI配置（使用ES模块导入）
import { 
    GEMINI_CONFIG, 
    AI_PROMPTS, 
    AI_CONTEXTS, 
    AI_FEATURES, 
    CACHE_CONFIG, 
    ERROR_HANDLING 
} from './ai-config.js';

class MDACBackground {
    constructor() {
        this.aiCache = new Map(); // AI响应缓存
        this.init();
    }

    /**
     * 初始化后台服务
     */
    init() {
        this.setupEventListeners();
        this.initializeStorage();
        console.log('MDAC AI扩展后台服务已启动');
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 扩展安装/更新事件
        chrome.runtime.onInstalled.addListener((details) => {
            this.handleInstalled(details);
        });

        // 消息传递
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true; // 保持消息通道开放
        });

        // 快捷键命令
        chrome.commands.onCommand.addListener((command) => {
            this.handleCommand(command);
        });

        // 标签页更新事件
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            this.handleTabUpdated(tabId, changeInfo, tab);
        });


    }

    /**
     * 初始化存储
     */
    async initializeStorage() {
        try {
            const storage = await chrome.storage.sync.get(['mdacSettings']);
            if (!storage.mdacSettings) {
                await chrome.storage.sync.set({
                    mdacSettings: {
                        aiEnabled: true,
                        geminiApiKey: 'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s', // 从原文件提取的默认密钥
                        aiModel: 'gemini-2.5-flash-lite-preview-06-17',
                        aiTemperature: 0.2,
                        debugMode: false
                    }
                });
                console.log('已初始化AI默认设置，包含Gemini API配置');
            }
        } catch (error) {
            console.error('初始化AI设置失败:', error);
        }
    }

    /**
     * 处理扩展安装/更新
     */
    async handleInstalled(details) {
        if (details.reason === 'install') {
            // 首次安装
            console.log('MDAC AI扩展首次安装');
            await this.showWelcomePage();
        } else if (details.reason === 'update') {
            // 扩展更新
            console.log('MDAC AI扩展已更新到版本:', chrome.runtime.getManifest().version);
            await this.handleUpdate(details.previousVersion);
        }
    }

    /**
     * 显示欢迎页面
     */
    async showWelcomePage() {
        try {
            await chrome.tabs.create({
                url: chrome.runtime.getURL('welcome.html')
            });
        } catch (error) {
            console.error('打开欢迎页面失败:', error);
        }
    }

    /**
     * 处理扩展更新
     */
    async handleUpdate(previousVersion) {
        // 处理版本迁移逻辑
        console.log(`从版本 ${previousVersion} 更新`);
    }

    /**
     * 处理消息传递
     */
    async handleMessage(message, sender, sendResponse) {
        try {
            switch (message.action) {
                case 'callGeminiAI':
                    const aiResponse = await this.callGeminiAI(message.prompt, message.context);
                    sendResponse({ success: true, data: aiResponse });
                    break;

                case 'callGeminiVision':
                    const visionResponse = await this.callGeminiVision(message.image, message.prompt);
                    sendResponse({ success: true, data: visionResponse });
                    break;

                case 'analyzeAPI':
                    const apiAnalysis = await this.analyzeAPI(message.requests);
                    sendResponse({ success: true, data: apiAnalysis });
                    break;



                case 'getUserData':
                    const userData = await this.getUserData();
                    sendResponse({ success: true, data: userData });
                    break;

                case 'parseContent':
                    const parseResult = await this.parseContentWithAI(message.content);
                    sendResponse({ success: true, data: parseResult });
                    break;

                case 'validateParsedData':
                    const validationResult = await this.validateParsedData(message.data);
                    sendResponse({ success: true, data: validationResult });
                    break;

                default:
                    sendResponse({ success: false, error: '未知的操作类型' });
            }
        } catch (error) {
            console.error('处理消息失败:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    /**
     * 处理快捷键命令
     */
    async handleCommand(command) {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            switch (command) {
                case 'fill-form':
                    if (tab.url && tab.url.includes('imigresen-online.imi.gov.my')) {
                        await chrome.tabs.sendMessage(tab.id, { action: 'quickFill' });
                    }
                    break;

                case 'analyze-api':
                    if (tab.url && tab.url.includes('imigresen-online.imi.gov.my')) {
                        await chrome.tabs.sendMessage(tab.id, { action: 'analyzeAPI' });
                    }
                    break;
            }
        } catch (error) {
            console.error('处理快捷键命令失败:', error);
        }
    }

    /**
     * 处理标签页更新
     */
    handleTabUpdated(tabId, changeInfo, tab) {
        if (changeInfo.status === 'complete' && tab.url && tab.url.includes('imigresen-online.imi.gov.my')) {
            // MDAC页面加载完成，更新扩展图标
            chrome.action.setBadgeText({
                text: '✓',
                tabId: tabId
            });
            chrome.action.setBadgeBackgroundColor({
                color: '#28a745',
                tabId: tabId
            });
        }
    }



    /**
     * 调用Gemini Vision API处理图片
     */
    async callGeminiVision(base64Image, prompt) {
        try {
            const settings = await chrome.storage.sync.get(['mdacSettings']);
            let apiKey = settings.mdacSettings?.geminiApiKey;

            if (!apiKey) {
                throw new Error('请先在设置中配置Gemini API密钥');
            }

            const requestBody = {
                contents: [{
                    parts: [
                        {
                            text: prompt
                        },
                        {
                            inline_data: {
                                mime_type: "image/jpeg",
                                data: base64Image
                            }
                        }
                    ]
                }],
                generationConfig: {
                    temperature: 0.1,
                    topK: 32,
                    topP: 1,
                    maxOutputTokens: 4096,
                }
            };

            const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKey}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(`Gemini Vision API错误: ${errorData.error?.message || response.statusText}`);
            }

            const data = await response.json();

            if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
                throw new Error('Gemini Vision API返回格式异常');
            }

            const result = data.candidates[0].content.parts[0].text;
            console.log('Gemini Vision API响应成功');

            return result;

        } catch (error) {
            console.error('Gemini Vision API调用失败:', error);
            throw error;
        }
    }

    /**
     * 调用Gemini AI API
     */
    async callGeminiAI(prompt, context = '') {
        const cacheKey = prompt + context;

        // 检查缓存
        if (CACHE_CONFIG.enabled && this.aiCache.has(cacheKey)) {
            console.log('使用缓存的AI响应');
            return this.aiCache.get(cacheKey);
        }

        try {
            const settings = await chrome.storage.sync.get(['mdacSettings']);
            let apiKey = settings.mdacSettings?.geminiApiKey;
            let model = settings.mdacSettings?.aiModel || GEMINI_CONFIG.DEFAULT_MODEL;
            let temperature = settings.mdacSettings?.aiTemperature || GEMINI_CONFIG.DEFAULT_GENERATION_CONFIG.temperature;

            // 如果没有配置API密钥，使用从原文件提取的默认密钥
            if (!apiKey) {
                apiKey = GEMINI_CONFIG.DEFAULT_API_KEY;
                console.log('使用默认Gemini API密钥');
            }

            const fullPrompt = context ? `${context}\n\n${prompt}` : prompt;
            const apiUrl = `${GEMINI_CONFIG.API_BASE_URL}/${model}:generateContent?key=${apiKey}`;

            const requestBody = {
                contents: [{
                    parts: [{
                        text: fullPrompt
                    }]
                }],
                generationConfig: {
                    ...GEMINI_CONFIG.DEFAULT_GENERATION_CONFIG,
                    temperature: temperature
                },
                safetySettings: GEMINI_CONFIG.SAFETY_SETTINGS
            };

            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                const errorData = await response.text();
                throw new Error(`API调用失败: ${response.status} - ${errorData}`);
            }

            const data = await response.json();

            if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
                throw new Error('API返回数据格式异常');
            }

            const result = data.candidates[0].content.parts[0].text;

            // 缓存结果
            if (CACHE_CONFIG.enabled) {
                this.aiCache.set(cacheKey, result);

                // 限制缓存大小
                if (this.aiCache.size > CACHE_CONFIG.maxSize) {
                    const firstKey = this.aiCache.keys().next().value;
                    this.aiCache.delete(firstKey);
                }
            }

            return result;
        } catch (error) {
            console.error('Gemini AI调用失败:', error);

            // 如果启用了错误重试
            if (ERROR_HANDLING.retryAttempts > 0) {
                console.log('尝试重新调用AI API...');
                // 这里可以实现重试逻辑
            }

            throw error;
        }
    }

    /**
     * 分析API请求
     */
    async analyzeAPI(requests) {
        // 实现API分析逻辑
        const analysis = {
            endpoints: [],
            security: {},
            parameters: {},
            recommendations: []
        };

        requests.forEach(request => {
            if (request.url.includes('/mdac/register')) {
                analysis.endpoints.push({
                    url: request.url,
                    method: request.method,
                    headers: request.headers,
                    body: request.body
                });
            }
        });

        return analysis;
    }









    /**
     * 使用AI解析内容
     */
    async parseContentWithAI(content) {
        try {
            const prompt = `请从以下文本中提取MDAC表单所需的信息：

文本内容：
${content}

请提取以下字段（如果存在）：
- name: 姓名（英文全名，如果是中文请翻译）
- passportNo: 护照号码
- dateOfBirth: 出生日期（DD/MM/YYYY格式）
- nationality: 国籍（3位代码，如CHN、USA、SGP等）
- sex: 性别（1=男性，2=女性）
- passportExpiry: 护照到期日期（DD/MM/YYYY格式）
- email: 电子邮箱
- confirmEmail: 确认邮箱（与email相同）
- countryCode: 国家代码（如+86、+60、+1等）
- mobileNo: 手机号码（不含国家代码）
- arrivalDate: 到达日期（DD/MM/YYYY格式）
- departureDate: 离开日期（DD/MM/YYYY格式）
- flightNo: 航班号或交通工具编号
- modeOfTravel: 旅行方式（AIR=航空，LAND=陆路，SEA=海路）
- lastPort: 最后港口（3位代码）
- accommodation: 住宿类型（01=酒店，02=朋友家，99=其他）
- address: 马来西亚地址行1（英文）
- address2: 马来西亚地址行2（英文，可选）
- state: 州代码（如14=吉隆坡，01=柔佛等）
- postcode: 邮政编码（5位数字）
- city: 城市代码

重要要求：
1. 请只返回JSON格式的数据，不要包含其他说明或markdown标记
2. 如果某个字段无法确定，请设为null
3. 对于中文信息，请自动翻译为英文
4. 确保日期格式严格为DD/MM/YYYY
5. 国籍和港口代码使用标准的3位ISO代码
6. 性别用数字：1=男性，2=女性
7. 地址必须翻译为英文且适合官方表单填写`;

            const context = '你是一个专业的信息提取专家，专门从文本中提取结构化数据。';

            const result = await this.callGeminiAI(prompt, context);

            // 清理和解析结果
            const cleanResult = result.replace(/```json|```/g, '').trim();
            const parsedData = JSON.parse(cleanResult);

            return parsedData;
        } catch (error) {
            console.error('AI内容解析失败:', error);
            throw error;
        }
    }

    /**
     * 验证解析后的数据
     */
    async validateParsedData(data) {
        try {
            const requiredFields = [
                'name', 'passportNo', 'dateOfBirth', 'nationality', 'sex',
                'passportExpiry', 'email', 'countryCode', 'mobileNo',
                'arrivalDate', 'departureDate', 'flightNo', 'modeOfTravel',
                'lastPort', 'accommodation', 'address', 'state', 'postcode', 'city'
            ];

            const missingFields = [];
            const invalidFields = [];
            let filledCount = 0;

            // 检查必填字段
            requiredFields.forEach(field => {
                const value = data[field];
                if (!value || value.toString().trim() === '') {
                    missingFields.push(field);
                } else {
                    filledCount++;

                    // 基本格式验证
                    if (!this.validateFieldFormat(field, value)) {
                        invalidFields.push(field);
                    }
                }
            });

            const completeness = Math.round((filledCount / requiredFields.length) * 100);
            const isComplete = missingFields.length === 0;
            const isValid = invalidFields.length === 0;

            return {
                isComplete,
                isValid,
                completeness,
                filledCount,
                totalFields: requiredFields.length,
                missingFields,
                invalidFields,
                canAutoFill: isComplete && isValid
            };
        } catch (error) {
            console.error('数据验证失败:', error);
            throw error;
        }
    }

    /**
     * 验证字段格式
     */
    validateFieldFormat(fieldName, value) {
        const validators = {
            email: (v) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v),
            dateOfBirth: (v) => /^\d{2}\/\d{2}\/\d{4}$/.test(v),
            passportExpiry: (v) => /^\d{2}\/\d{2}\/\d{4}$/.test(v),
            arrivalDate: (v) => /^\d{2}\/\d{2}\/\d{4}$/.test(v),
            departureDate: (v) => /^\d{2}\/\d{2}\/\d{4}$/.test(v),
            postcode: (v) => /^\d{5}$/.test(v),
            sex: (v) => ['1', '2'].includes(v.toString()),
            countryCode: (v) => /^\+\d{1,4}$/.test(v),
            mobileNo: (v) => /^\d{8,15}$/.test(v.replace(/\s/g, '')),
            nationality: (v) => /^[A-Z]{3}$/.test(v),
            lastPort: (v) => /^[A-Z]{3}$/.test(v)
        };

        const validator = validators[fieldName];
        return validator ? validator(value) : true;
    }
}

// 初始化后台服务
new MDACBackground();
