/**
 * MDAC AI智能填充工具 - 设置页面脚本
 * 处理扩展的设置和配置管理
 */

class MDACOptions {
    constructor() {
        this.settings = {};
        this.init();
    }

    /**
     * 初始化设置页面
     */
    async init() {
        this.setupEventListeners();
        await this.loadSettings();
        this.updateUI();
        this.updateDataStats();
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 标签切换
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.addEventListener('click', (e) => this.switchTab(e.target.dataset.tab));
        });

        // 设置保存
        document.getElementById('saveSettings').addEventListener('click', () => this.saveSettings());
        document.getElementById('resetSettings').addEventListener('click', () => this.resetSettings());

        // AI配置
        document.getElementById('toggleApiKey').addEventListener('click', () => this.toggleApiKeyVisibility());
        document.getElementById('testAI').addEventListener('click', () => this.testAIConnection());





        // 链接按钮
        document.getElementById('helpLink').addEventListener('click', () => this.openHelp());
        document.getElementById('feedbackLink').addEventListener('click', () => this.openFeedback());

        // 实时保存AI设置
        document.getElementById('aiEnabled').addEventListener('change', () => this.autoSave());
    }

    /**
     * 切换标签页
     */
    switchTab(tabName) {
        // 更新标签按钮状态
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // 更新内容显示
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(tabName).classList.add('active');
    }

    /**
     * 加载设置
     */
    async loadSettings() {
        try {
            const result = await chrome.storage.sync.get(['mdacSettings']);
            this.settings = result.mdacSettings || this.getDefaultSettings();
        } catch (error) {
            console.error('加载设置失败:', error);
            this.settings = this.getDefaultSettings();
        }
    }

    /**
     * 获取默认AI设置
     */
    getDefaultSettings() {
        return {
            geminiApiKey: 'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s', // 从原文件提取的默认密钥
            aiEnabled: true,
            aiModel: 'gemini-2.5-flash-lite-preview-06-17',
            aiTemperature: 0.2,
            debugMode: false
        };
    }

    /**
     * 更新AI配置UI显示
     */
    updateUI() {
        // AI配置
        document.getElementById('geminiApiKey').value = this.settings.geminiApiKey || '';
        document.getElementById('aiEnabled').checked = this.settings.aiEnabled !== false;
        document.getElementById('aiModel').value = this.settings.aiModel || 'gemini-2.5-flash-lite-preview-06-17';
        document.getElementById('aiTemperature').value = this.settings.aiTemperature || 0.2;
        document.getElementById('debugMode').checked = this.settings.debugMode || false;
    }

    /**
     * 保存AI设置
     */
    async saveSettings() {
        try {
            // 收集AI相关设置
            this.settings = {
                geminiApiKey: document.getElementById('geminiApiKey').value,
                aiEnabled: document.getElementById('aiEnabled').checked,
                aiModel: document.getElementById('aiModel').value,
                aiTemperature: parseFloat(document.getElementById('aiTemperature').value),
                debugMode: document.getElementById('debugMode').checked
            };

            await chrome.storage.sync.set({ mdacSettings: this.settings });
            this.showStatus('AI设置已保存', 'success');
        } catch (error) {
            console.error('保存AI设置失败:', error);
            this.showStatus('保存失败: ' + error.message, 'error');
        }
    }

    /**
     * 自动保存AI设置
     */
    async autoSave() {
        try {
            const quickSettings = {
                aiEnabled: document.getElementById('aiEnabled').checked
            };

            this.settings = { ...this.settings, ...quickSettings };
            await chrome.storage.sync.set({ mdacSettings: this.settings });
        } catch (error) {
            console.error('自动保存失败:', error);
        }
    }

    /**
     * 重置设置
     */
    async resetSettings() {
        if (confirm('确定要重置所有设置吗？此操作不可撤销。')) {
            try {
                this.settings = this.getDefaultSettings();
                await chrome.storage.sync.set({ mdacSettings: this.settings });
                this.updateUI();
                this.showStatus('设置已重置', 'success');
            } catch (error) {
                console.error('重置设置失败:', error);
                this.showStatus('重置失败: ' + error.message, 'error');
            }
        }
    }

    /**
     * 切换API密钥可见性
     */
    toggleApiKeyVisibility() {
        const input = document.getElementById('geminiApiKey');
        const button = document.getElementById('toggleApiKey');
        
        if (input.type === 'password') {
            input.type = 'text';
            button.textContent = '🙈';
        } else {
            input.type = 'password';
            button.textContent = '👁️';
        }
    }

    /**
     * 测试AI连接
     */
    async testAIConnection() {
        const button = document.getElementById('testAI');
        const result = document.getElementById('aiTestResult');
        
        button.disabled = true;
        button.textContent = '测试中...';
        result.style.display = 'none';

        try {
            const apiKey = document.getElementById('geminiApiKey').value;
            if (!apiKey) {
                throw new Error('请先输入API密钥');
            }

            const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite-preview-06-17:generateContent?key=${apiKey}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    contents: [{
                        parts: [{
                            text: '你好，请回复"连接成功"'
                        }]
                    }]
                })
            });

            if (response.ok) {
                result.className = 'test-result success';
                result.textContent = '✅ AI连接测试成功！';
            } else {
                throw new Error(`API调用失败: ${response.status}`);
            }
        } catch (error) {
            result.className = 'test-result error';
            result.textContent = '❌ 连接失败: ' + error.message;
        } finally {
            button.disabled = false;
            button.textContent = '测试AI连接';
            result.style.display = 'block';
        }
    }









    /**
     * 显示状态消息
     */
    showStatus(message, type = 'success') {
        const status = document.getElementById('saveStatus');
        status.textContent = message;
        status.className = `status ${type}`;
        
        setTimeout(() => {
            status.textContent = '';
            status.className = 'status';
        }, 3000);
    }

    /**
     * 打开帮助页面
     */
    openHelp() {
        chrome.tabs.create({
            url: chrome.runtime.getURL('help.html')
        });
    }

    /**
     * 打开反馈页面
     */
    openFeedback() {
        chrome.tabs.create({
            url: 'https://github.com/mdac-ai/feedback/issues'
        });
    }


}

// 初始化设置页面
document.addEventListener('DOMContentLoaded', () => {
    new MDACOptions();
});
