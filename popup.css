/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    width: 380px;
    min-height: 500px;
    font-family: 'Microsoft YaHei', 'Segoe UI', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    font-size: 14px;
    line-height: 1.4;
}

.container {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
}

.logo img {
    width: 24px;
    height: 24px;
}

.logo h1 {
    font-size: 16px;
    font-weight: 600;
}

.ai-status {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
}

.status-dot {
    width: 8px;
    height: 8px;
    background: #4ade80;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* 网站检测区域 */
.site-detection {
    padding: 12px 16px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.detection-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    color: #666;
}

.detection-status.detected {
    color: #28a745;
}

.detection-status.not-detected {
    color: #dc3545;
}

/* 主要内容区域 */
.main-content {
    padding: 16px;
}

/* 快速操作按钮 */
.quick-actions {
    display: flex;
    gap: 6px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.action-btn {
    flex: 1;
    min-width: 100px;
    padding: 10px 6px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 3px;
    font-size: 11px;
    transition: all 0.2s ease;
}

.action-btn.primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.action-btn.secondary {
    background: #f8f9fa;
    color: #666;
    border: 1px solid #dee2e6;
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.action-btn .icon {
    font-size: 16px;
}

.action-btn .text {
    font-weight: 500;
}

/* AI功能展示 */
.ai-features {
    margin-bottom: 20px;
}

.ai-features h3 {
    font-size: 14px;
    margin-bottom: 12px;
    color: #333;
}

.feature-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.feature-item {
    display: flex;
    align-items: center;
    padding: 10px;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    background: white;
    transition: all 0.2s ease;
}

.feature-item:hover {
    background: #f8f9fa;
    border-color: #667eea;
}

.feature-icon {
    font-size: 18px;
    margin-right: 10px;
}

.feature-info {
    flex: 1;
}

.feature-info h4 {
    font-size: 13px;
    margin-bottom: 2px;
    color: #333;
}

.feature-info p {
    font-size: 11px;
    color: #666;
}

.feature-status {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #ddd;
    transition: background 0.2s ease;
}

.feature-status.active {
    background: #28a745;
}

/* AI助手 */
.ai-assistant {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 16px;
}

.ai-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.ai-title {
    font-size: 13px;
    font-weight: 600;
}

.ai-indicator {
    width: 6px;
    height: 6px;
    background: #4ade80;
    border-radius: 50%;
    margin-left: auto;
}

.ai-suggestions {
    font-size: 12px;
    opacity: 0.9;
    line-height: 1.4;
}

/* 底部按钮 */
.footer {
    display: flex;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
}

.footer-btn {
    flex: 1;
    padding: 12px 8px;
    border: none;
    background: none;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    font-size: 10px;
    color: #666;
    transition: all 0.2s ease;
}

.footer-btn:hover {
    background: #e9ecef;
    color: #333;
}

.footer-btn .icon {
    font-size: 14px;
}

/* 模态对话框 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 400px;
    max-height: 80%;
    overflow: hidden;
}

.modal-header {
    padding: 16px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: 16px;
    color: #333;
}

.modal-close {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-body {
    padding: 16px;
    max-height: 300px;
    overflow-y: auto;
}

.modal-footer {
    padding: 16px;
    border-top: 1px solid #e9ecef;
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
}

.btn.primary {
    background: #667eea;
    color: white;
}

.btn.secondary {
    background: #f8f9fa;
    color: #666;
    border: 1px solid #dee2e6;
}

.btn:hover {
    opacity: 0.9;
    transform: translateY(-1px);
}

/* 内容输入区域 - 直接可见 */
.content-input-section {
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 16px;
    border: 1px solid #e9ecef;
    overflow: hidden;
}

.input-header {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.input-header h3 {
    font-size: 14px;
    margin: 0;
}

.input-status {
    font-size: 11px;
    opacity: 0.9;
}

.input-area {
    padding: 16px;
}

.input-area label {
    display: block;
    font-size: 12px;
    font-weight: 500;
    margin-bottom: 8px;
    color: #333;
}

.input-area textarea {
    width: 100%;
    min-height: 140px;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 12px;
    font-family: inherit;
    resize: vertical;
    line-height: 1.4;
    margin-bottom: 12px;
}

.input-area textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

/* 图片上传区域 */
.image-upload-area {
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 6px;
    padding: 16px;
    text-align: center;
    margin-bottom: 12px;
    transition: all 0.2s ease;
}

.image-upload-area:hover {
    border-color: #667eea;
    background: #f0f4ff;
}

.upload-btn {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.upload-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.upload-hint {
    font-size: 10px;
    color: #666;
    margin-top: 6px;
}

/* 补充信息输入区域 */
.supplement-input-section {
    background: #f0f8ff;
    border-radius: 8px;
    margin-bottom: 16px;
    border: 1px solid #b3d9ff;
    overflow: hidden;
}

.supplement-header {
    background: linear-gradient(135deg, #4a90e2, #357abd);
    color: white;
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.supplement-header h3 {
    font-size: 14px;
    margin: 0;
}

.supplement-status {
    font-size: 11px;
    opacity: 0.9;
}

.supplement-area {
    padding: 16px;
}

.supplement-area label {
    display: block;
    font-size: 12px;
    font-weight: 500;
    margin-bottom: 8px;
    color: #333;
}

.supplement-area textarea {
    width: 100%;
    min-height: 120px;
    padding: 12px;
    border: 1px solid #b3d9ff;
    border-radius: 6px;
    font-size: 12px;
    font-family: inherit;
    resize: vertical;
    line-height: 1.4;
    margin-bottom: 12px;
    background: #fafbff;
}

.supplement-area textarea:focus {
    outline: none;
    border-color: #4a90e2;
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.1);
    background: white;
}

.supplement-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.supplement-actions .btn {
    padding: 6px 12px;
    font-size: 11px;
}

.btn.info {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn.info:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);
}

/* 合并数据预览样式 */
.merged-data-preview {
    max-height: 400px;
    overflow-y: auto;
    font-size: 12px;
}

.merged-data-preview h4 {
    margin: 0 0 16px 0;
    color: #333;
    font-size: 14px;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 8px;
}

.data-source-section {
    margin-bottom: 16px;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    overflow: hidden;
}

.data-source-section h5 {
    margin: 0;
    padding: 8px 12px;
    font-size: 12px;
    font-weight: 600;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.data-source-section.final-result h5 {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.data-fields {
    padding: 8px;
}

.field-item {
    display: flex;
    align-items: center;
    padding: 4px 8px;
    margin-bottom: 4px;
    border-radius: 4px;
    font-size: 11px;
}

.field-item.ai-data {
    background: #e3f2fd;
    border-left: 3px solid #2196f3;
}

.field-item.supplement-data {
    background: #f0f8ff;
    border-left: 3px solid #4a90e2;
}

.field-item.supplement-data.overridden {
    background: #fff3cd;
    border-left: 3px solid #ffc107;
    opacity: 0.7;
}

.field-item.merged-data {
    background: #d4edda;
    border-left: 3px solid #28a745;
}

.field-label {
    font-weight: 600;
    min-width: 80px;
    color: #333;
}

.field-value {
    flex: 1;
    margin-left: 8px;
    color: #555;
}

.override-note {
    font-size: 10px;
    color: #856404;
    font-style: italic;
    margin-left: 8px;
}

.input-actions {
    display: flex;
    gap: 8px;
    margin-top: 10px;
}

.input-actions .btn {
    flex: 1;
    padding: 8px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    transition: all 0.2s ease;
}

.input-actions .btn.primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.input-actions .btn.secondary {
    background: #f8f9fa;
    color: #666;
    border: 1px solid #dee2e6;
}

.input-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 解析状态 */
.parsing-status {
    background: white;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 16px;
    text-align: center;
}

.status-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-bottom: 12px;
}

.loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.status-text {
    font-size: 12px;
    color: #666;
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: #f0f0f0;
    border-radius: 2px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 2px;
    transition: width 0.3s ease;
    animation: progressPulse 2s ease-in-out infinite;
}

@keyframes progressPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* 解析结果 */
.parse-results {
    background: white;
    border-radius: 6px;
    padding: 16px;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e9ecef;
}

.results-header h4 {
    font-size: 13px;
    margin: 0;
    color: #333;
}

.completeness-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
}

.completeness-text {
    font-size: 11px;
    color: #666;
    min-width: 60px;
}

.completeness-bar {
    width: 80px;
    height: 6px;
    background: #f0f0f0;
    border-radius: 3px;
    overflow: hidden;
}

.completeness-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    border-radius: 3px;
    transition: width 0.3s ease;
}

/* 提取的数据 */
.extracted-data {
    margin-bottom: 16px;
}

.data-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 0;
    border-bottom: 1px solid #f0f0f0;
    font-size: 11px;
}

.data-item:last-child {
    border-bottom: none;
}

.data-label {
    font-weight: 500;
    color: #333;
    min-width: 80px;
}

.data-value {
    color: #666;
    text-align: right;
    flex: 1;
    margin-left: 8px;
    word-break: break-word;
}

.data-value.empty {
    color: #ccc;
    font-style: italic;
}

.data-value.translated {
    color: #667eea;
}

/* 缺失字段 */
.missing-fields {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 12px;
    margin-bottom: 16px;
}

.missing-fields h5 {
    font-size: 12px;
    margin: 0 0 8px 0;
    color: #856404;
}

.missing-list {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
}

.missing-item {
    background: #ffeaa7;
    color: #856404;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: 500;
}

/* 结果操作按钮 */
.results-actions {
    display: flex;
    gap: 6px;
}

.results-actions .btn {
    flex: 1;
    padding: 8px 6px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
    transition: all 0.2s ease;
}

.results-actions .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.results-actions .btn:not(:disabled):hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.results-actions .btn.primary {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.results-actions .btn.primary:disabled {
    background: #6c757d;
}

.results-actions .btn.secondary {
    background: #f8f9fa;
    color: #666;
    border: 1px solid #dee2e6;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 4px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
