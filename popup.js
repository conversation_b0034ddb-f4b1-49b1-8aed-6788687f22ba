/**
 * MDAC AI智能填充工具 - Popup脚本
 * 处理扩展弹窗的用户界面和交互逻辑
 */

class MDACPopup {
    constructor() {
        this.currentTab = null;
        this.isMDACPage = false;
        this.aiStatus = 'ready';
        this.parsedData = null;
        this.supplementData = null; // 持久化的补充信息
        this.mergedData = null; // 合并后的完整数据
        this.dataPreviewManager = null;
        this.errorRecoveryManager = null;
        this.fillMonitor = null;

        this.init();
    }

    /**
     * 初始化弹窗
     */
    async init() {
        await this.getCurrentTab();
        await this.detectMDACPage();
        this.setupEventListeners();
        await this.loadUserSettings();
        this.initializeDataPreviewManager();
        this.initializeErrorRecoveryManager();
        this.initializeFillMonitor();
        this.initializeSupplementInput();
        this.updateUI();
        await this.testAIConnection();
    }

    /**
     * 获取当前标签页
     */
    async getCurrentTab() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            this.currentTab = tab;
        } catch (error) {
            console.error('获取当前标签页失败:', error);
        }
    }

    /**
     * 检测是否为MDAC页面
     */
    async detectMDACPage() {
        if (!this.currentTab) return;

        const detectionStatus = document.getElementById('detectionStatus');
        
        if (this.currentTab.url && this.currentTab.url.includes('imigresen-online.imi.gov.my')) {
            this.isMDACPage = true;
            detectionStatus.className = 'detection-status detected';
            detectionStatus.innerHTML = '<span class="icon">✅</span><span class="text">已检测到MDAC网站</span>';
        } else {
            this.isMDACPage = false;
            detectionStatus.className = 'detection-status not-detected';
            detectionStatus.innerHTML = '<span class="icon">❌</span><span class="text">请先打开MDAC网站</span>';
        }
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 快速操作按钮
        document.getElementById('quickFillBtn').addEventListener('click', () => this.quickFill());
        document.getElementById('openFormBtn').addEventListener('click', () => this.openFormEditor());

        // 内容解析事件
        document.getElementById('parseContentBtn').addEventListener('click', () => this.parseContent());
        document.getElementById('clearContentBtn').addEventListener('click', () => this.clearContent());
        document.getElementById('editDataBtn').addEventListener('click', () => this.editParsedData());
        document.getElementById('saveTemplateFromParseBtn').addEventListener('click', () => this.copyParsedData());
        document.getElementById('fillFormBtn').addEventListener('click', () => this.fillFormWithParsedData());

        // 图片上传事件
        document.getElementById('uploadImageBtn').addEventListener('click', () => this.triggerImageUpload());
        document.getElementById('imageInput').addEventListener('change', (e) => this.handleImageUpload(e));

        // 补充信息事件
        document.getElementById('supplementInput').addEventListener('input', (e) => this.handleSupplementInput(e));
        document.getElementById('clearSupplementBtn').addEventListener('click', () => this.clearSupplementData());
        document.getElementById('previewMergedDataBtn').addEventListener('click', () => this.previewMergedData());

        // AI功能状态更新
        this.updateAIFeatureStatus();

        // 底部按钮
        document.getElementById('aiSettingsBtn').addEventListener('click', () => this.openAISettings());
        document.getElementById('helpBtn').addEventListener('click', () => this.showHelp());

        // 模态对话框
        document.getElementById('modalClose').addEventListener('click', () => this.closeModal());
        document.getElementById('modalCancel').addEventListener('click', () => this.closeModal());
        document.getElementById('modalConfirm').addEventListener('click', () => this.confirmModal());
    }

    /**
     * 加载用户设置
     */
    async loadUserSettings() {
        try {
            const settings = await chrome.storage.sync.get(['mdacSettings']);
            // AI设置已在background.js中初始化
            console.log('AI设置已加载');
        } catch (error) {
            console.error('加载设置失败:', error);
        }
    }

    /**
     * 初始化数据预览管理器
     */
    initializeDataPreviewManager() {
        try {
            this.dataPreviewManager = new DataPreviewManager();
            console.log('✅ 数据预览管理器初始化完成');
        } catch (error) {
            console.error('❌ 数据预览管理器初始化失败:', error);
        }
    }

    /**
     * 初始化错误恢复管理器
     */
    async initializeErrorRecoveryManager() {
        try {
            this.errorRecoveryManager = new ErrorRecoveryManager();
            await this.errorRecoveryManager.loadErrorHistory();
            console.log('✅ 错误恢复管理器初始化完成');
        } catch (error) {
            console.error('❌ 错误恢复管理器初始化失败:', error);
        }
    }



    /**
     * 初始化填充监控器
     */
    async initializeFillMonitor() {
        try {
            this.fillMonitor = new FillMonitor();
            console.log('✅ 填充监控器初始化完成');
        } catch (error) {
            console.error('❌ 填充监控器初始化失败:', error);
        }
    }

    /**
     * 更新UI状态
     */
    updateUI() {
        // 更新按钮状态
        const quickFillBtn = document.getElementById('quickFillBtn');
        const openFormBtn = document.getElementById('openFormBtn');

        if (this.isMDACPage) {
            quickFillBtn.disabled = false;
            openFormBtn.disabled = false;
        } else {
            quickFillBtn.disabled = true;
            openFormBtn.disabled = true;
        }

        // 更新AI状态
        this.updateAIStatus();
    }

    /**
     * 更新AI状态显示
     */
    updateAIStatus() {
        const aiStatus = document.getElementById('aiStatus');
        const aiIndicator = document.getElementById('aiIndicator');
        
        switch (this.aiStatus) {
            case 'ready':
                aiStatus.innerHTML = '<span class="status-dot"></span><span class="status-text">AI就绪</span>';
                aiIndicator.style.background = '#4ade80';
                break;
            case 'working':
                aiStatus.innerHTML = '<span class="status-dot"></span><span class="status-text">AI工作中</span>';
                aiIndicator.style.background = '#fbbf24';
                break;
            case 'error':
                aiStatus.innerHTML = '<span class="status-dot"></span><span class="status-text">AI错误</span>';
                aiIndicator.style.background = '#ef4444';
                break;
        }
    }

    /**
     * 更新AI功能状态
     */
    updateAIFeatureStatus() {
        const features = document.querySelectorAll('.feature-status');
        features.forEach(status => {
            status.classList.add('active');
        });
    }

    /**
     * 快速填充
     */
    async quickFill() {
        if (!this.isMDACPage) {
            this.showMessage('请先打开MDAC网站', 'error');
            return;
        }

        this.aiStatus = 'working';
        this.updateAIStatus();

        try {
            // 向content script发送AI智能填充指令
            await chrome.tabs.sendMessage(this.currentTab.id, {
                action: 'quickFill'
            });

            this.aiStatus = 'ready';
            this.updateAIStatus();
            this.showMessage('AI智能填充完成', 'success');
        } catch (error) {
            console.error('快速填充失败:', error);
            this.aiStatus = 'error';
            this.updateAIStatus();
            this.showMessage('填充失败，请重试', 'error');
        }
    }

    /**
     * 打开表单编辑器
     */
    openFormEditor() {
        chrome.tabs.create({
            url: chrome.runtime.getURL('form-editor.html')
        });
    }

    /**
     * 打开AI设置页面
     */
    openAISettings() {
        chrome.runtime.openOptionsPage();
    }

    /**
     * 显示帮助信息
     */
    showHelp() {
        this.showModal('AI智能分析帮助', `
            <div class="help-content">
                <h4>🧠 智能内容解析</h4>
                <p>1. 点击"智能解析"按钮</p>
                <p>2. 粘贴任意格式的文本内容</p>
                <p>3. AI自动提取表单数据</p>
                <p>4. 验证完整性后一键填充</p>

                <h4>✅ AI数据验证</h4>
                <p>• 自动验证数据格式和逻辑</p>
                <p>• 提供智能优化建议</p>
                <p>• 实时错误检测和修正</p>

                <h4>🌐 智能地址翻译</h4>
                <p>• 自动识别中文地址</p>
                <p>• 翻译为标准英文格式</p>
                <p>• 适配马来西亚官方要求</p>

                <h4>🤖 AI助手功能</h4>
                <p>• 实时状态监控</p>
                <p>• 智能建议和提示</p>
                <p>• 错误诊断和解决方案</p>
            </div>
        `);
    }





    /**
     * 显示消息提示
     */
    showMessage(message, type = 'info') {
        const aiSuggestions = document.getElementById('aiSuggestions');
        const icons = {
            'success': '✅',
            'error': '❌',
            'info': 'ℹ️',
            'warning': '⚠️'
        };
        
        aiSuggestions.innerHTML = `${icons[type]} ${message}`;
        
        // 3秒后恢复默认状态
        setTimeout(() => {
            aiSuggestions.innerHTML = '准备就绪，等待您的指令...';
        }, 3000);
    }

    /**
     * 显示模态对话框
     */
    showModal(title, content, options = {}) {
        document.getElementById('modalTitle').textContent = title;
        document.getElementById('modalBody').innerHTML = content;

        // 更新按钮文本
        const confirmBtn = document.getElementById('modalConfirm');
        const cancelBtn = document.getElementById('modalCancel');

        if (confirmBtn && options.confirmText) {
            confirmBtn.textContent = options.confirmText;
        }
        if (cancelBtn && options.cancelText) {
            cancelBtn.textContent = options.cancelText;
        }

        // 设置确认回调
        if (options.onConfirm) {
            this.modalConfirmCallback = options.onConfirm;
        } else {
            this.modalConfirmCallback = null;
        }

        document.getElementById('modal').classList.add('show');
    }

    /**
     * 关闭模态对话框
     */
    closeModal() {
        document.getElementById('modal').classList.remove('show');
    }

    /**
     * 确认模态对话框
     */
    confirmModal() {
        // 执行自定义确认回调
        if (this.modalConfirmCallback) {
            this.modalConfirmCallback();
            this.modalConfirmCallback = null;
        }
        this.closeModal();
    }

    /**
     * 初始化补充信息输入功能
     */
    async initializeSupplementInput() {
        try {
            // 从本地存储加载补充信息
            await this.loadSupplementData();
            console.log('✅ 补充信息输入功能初始化完成');
        } catch (error) {
            console.error('❌ 补充信息输入功能初始化失败:', error);
        }
    }

    /**
     * 从本地存储加载补充信息
     */
    async loadSupplementData() {
        try {
            const result = await chrome.storage.local.get(['mdacSupplementData']);
            const supplementData = result.mdacSupplementData || {};

            this.supplementData = supplementData;

            // 更新UI显示
            const supplementInput = document.getElementById('supplementInput');
            if (supplementInput && supplementData.rawText) {
                supplementInput.value = supplementData.rawText;
            }

            this.updateSupplementStatus();

        } catch (error) {
            console.error('加载补充信息失败:', error);
            this.supplementData = {};
        }
    }

    /**
     * 保存补充信息到本地存储
     */
    async saveSupplementData(rawText, parsedFields = {}) {
        try {
            const supplementData = {
                rawText: rawText,
                parsedFields: parsedFields,
                lastUpdated: new Date().toISOString()
            };

            await chrome.storage.local.set({ mdacSupplementData: supplementData });
            this.supplementData = supplementData;
            this.updateSupplementStatus();

        } catch (error) {
            console.error('保存补充信息失败:', error);
        }
    }

    /**
     * 更新补充信息状态显示
     */
    updateSupplementStatus() {
        const statusElement = document.getElementById('supplementStatus');
        if (!statusElement) return;

        const statusText = statusElement.querySelector('.status-text');
        if (!statusText) return;

        if (this.supplementData && this.supplementData.rawText) {
            const fieldCount = Object.keys(this.supplementData.parsedFields || {}).length;
            const textLength = this.supplementData.rawText.length;

            if (fieldCount > 0) {
                statusText.textContent = `已保存${fieldCount}个字段，${textLength}个字符`;
            } else {
                statusText.textContent = `已保存${textLength}个字符，待解析`;
            }
        } else {
            statusText.textContent = '暂无保存的补充信息';
        }
    }

    /**
     * 处理补充信息输入
     */
    async handleSupplementInput(event) {
        const rawText = event.target.value;

        // 实时保存到本地存储
        if (rawText.trim()) {
            // 简单解析补充信息中的字段
            const parsedFields = this.parseSupplementFields(rawText);
            await this.saveSupplementData(rawText, parsedFields);
        } else {
            // 如果内容为空，清除存储
            await this.clearSupplementData();
        }
    }

    /**
     * 简单解析补充信息中的字段
     */
    parseSupplementFields(text) {
        const fields = {};
        const lines = text.split('\n');

        // 定义字段模式匹配
        const patterns = {
            email: /(?:邮箱|email|邮件)[:：]\s*([^\s\n]+@[^\s\n]+)/i,
            countryCode: /(?:国家代码|country\s*code)[:：]\s*(\+?\d+)/i,
            mobileNo: /(?:电话|手机|phone|mobile)[:：]\s*([+\d\s-]+)/i,
            flightNo: /(?:航班号|flight\s*no|航班)[:：]\s*([A-Z0-9]+)/i,
            arrivalDate: /(?:到达日期|arrival\s*date|到达时间)[:：]\s*([^\n]+)/i,
            departureDate: /(?:离开日期|departure\s*date|离开时间)[:：]\s*([^\n]+)/i,
            accommodation: /(?:住宿类型|accommodation|住宿)[:：]\s*([^\n]+)/i,
            address: /(?:地址|address)[:：]\s*([^\n]+)/i,
            state: /(?:州|state|省)[:：]\s*([^\n]+)/i,
            postcode: /(?:邮编|postcode|邮政编码)[:：]\s*([^\n]+)/i,
            city: /(?:城市|city)[:：]\s*([^\n]+)/i,
            modeOfTravel: /(?:旅行方式|travel\s*mode|交通方式)[:：]\s*([^\n]+)/i,
            lastPort: /(?:最后港口|last\s*port|上一个港口)[:：]\s*([^\n]+)/i
        };

        // 对整个文本进行模式匹配
        Object.keys(patterns).forEach(fieldKey => {
            const match = text.match(patterns[fieldKey]);
            if (match && match[1]) {
                fields[fieldKey] = match[1].trim();
            }
        });

        return fields;
    }

    /**
     * 清空补充信息
     */
    async clearSupplementData() {
        try {
            await chrome.storage.local.remove(['mdacSupplementData']);
            this.supplementData = {};

            // 清空输入框
            const supplementInput = document.getElementById('supplementInput');
            if (supplementInput) {
                supplementInput.value = '';
            }

            this.updateSupplementStatus();
            this.showMessage('补充信息已清空', 'info');

        } catch (error) {
            console.error('清空补充信息失败:', error);
            this.showMessage('清空补充信息失败: ' + error.message, 'error');
        }
    }

    /**
     * 预览合并后的数据
     */
    previewMergedData() {
        // 合并AI解析数据和补充信息
        const mergedData = this.mergeDataSources();

        if (!mergedData || Object.keys(mergedData).length === 0) {
            this.showMessage('没有可预览的数据，请先进行AI解析或输入补充信息', 'warning');
            return;
        }

        // 显示合并数据预览
        this.showMergedDataPreview(mergedData);
    }

    /**
     * 合并AI解析数据和补充信息
     */
    mergeDataSources() {
        const merged = {};

        // 首先添加补充信息（优先级较低）
        if (this.supplementData && this.supplementData.parsedFields) {
            Object.assign(merged, this.supplementData.parsedFields);
        }

        // 然后添加AI解析数据（优先级较高，会覆盖补充信息中的同名字段）
        if (this.parsedData) {
            Object.assign(merged, this.parsedData);
        }

        this.mergedData = merged;
        return merged;
    }

    /**
     * 测试AI连接
     */
    async testAIConnection() {
        try {
            this.aiStatus = 'working';
            this.updateAIStatus();

            const response = await chrome.runtime.sendMessage({
                action: 'callGeminiAI',
                prompt: '请回复"AI连接正常"',
                context: '这是一个连接测试'
            });

            if (response.success && response.data) {
                this.aiStatus = 'ready';
                this.updateAIStatus();
                this.showMessage('🤖 AI助手已就绪！Gemini AI连接正常，所有智能功能可用。', 'success');
            } else {
                throw new Error('AI响应异常');
            }
        } catch (error) {
            console.warn('Gemini AI连接测试失败:', error);
            this.aiStatus = 'error';
            this.updateAIStatus();
            this.showMessage('⚠️ AI服务连接异常，将使用基础验证功能。', 'warning');
        }
    }

    /**
     * 获取AI状态描述
     */
    getAIStatusDescription() {
        switch (this.aiStatus) {
            case 'ready':
                return '🤖 AI助手已就绪！所有智能功能可用。\n\n💡 功能包括：\n• AI智能验证和建议\n• 实时内容解析\n• 智能地址翻译\n• 表单优化建议';
            case 'working':
                return '🔄 AI正在工作中...';
            case 'error':
                return '⚠️ AI服务暂时不可用\n\n📝 基础功能仍然可用：\n• 表单验证和格式检查\n• 自动填充和数据保存\n• 模板管理和历史记录';
            default:
                return '准备就绪，等待您的指令...';
        }
    }

    /**
     * 更新AI建议显示
     */
    updateAISuggestions(message) {
        const aiSuggestions = document.getElementById('aiSuggestions');
        if (aiSuggestions) {
            aiSuggestions.innerHTML = message || this.getAIStatusDescription();
        }
    }



    /**
     * 清空内容输入
     */
    clearContent() {
        const contentInput = document.getElementById('contentInput');
        const imageInput = document.getElementById('imageInput');
        if (contentInput) {
            contentInput.value = '';
        }
        if (imageInput) {
            imageInput.value = '';
        }
        this.clearParseResults();
        this.showMessage('内容已清空', 'info');
    }

    /**
     * 触发图片上传
     */
    triggerImageUpload() {
        const imageInput = document.getElementById('imageInput');
        if (imageInput) {
            imageInput.click();
        }
    }

    /**
     * 处理图片上传
     */
    async handleImageUpload(event) {
        const file = event.target.files[0];
        if (!file) return;

        // 验证文件类型
        if (!file.type.startsWith('image/')) {
            this.showMessage('请选择有效的图片文件', 'error');
            return;
        }

        // 验证文件大小（限制为5MB）
        if (file.size > 5 * 1024 * 1024) {
            this.showMessage('图片文件大小不能超过5MB', 'error');
            return;
        }

        try {
            this.showMessage('正在处理图片...', 'info');

            // 将图片转换为base64
            const base64Image = await this.fileToBase64(file);

            // 调用Gemini Vision API处理图片
            const response = await chrome.runtime.sendMessage({
                action: 'callGeminiVision',
                image: base64Image,
                prompt: '请提取这张图片中的所有文字信息，特别是个人信息、护照信息、预订信息等。请保持原始格式输出。'
            });

            if (response.success) {
                // 将提取的文字添加到文本框中
                const contentInput = document.getElementById('contentInput');
                if (contentInput) {
                    const existingContent = contentInput.value.trim();
                    const newContent = existingContent ?
                        `${existingContent}\n\n--- 图片提取内容 ---\n${response.data}` :
                        response.data;
                    contentInput.value = newContent;
                }
                this.showMessage('图片文字提取成功！', 'success');
            } else {
                throw new Error('图片处理失败');
            }
        } catch (error) {
            console.error('图片处理失败:', error);
            this.showMessage('图片处理失败: ' + error.message, 'error');
        }
    }

    /**
     * 将文件转换为base64
     */
    fileToBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => {
                // 移除data:image/jpeg;base64,前缀
                const base64 = reader.result.split(',')[1];
                resolve(base64);
            };
            reader.onerror = reject;
            reader.readAsDataURL(file);
        });
    }

    /**
     * 清空解析结果
     */
    clearParseResults() {
        const parsingStatus = document.getElementById('parsingStatus');
        const parseResults = document.getElementById('parseResults');

        if (parsingStatus) parsingStatus.style.display = 'none';
        if (parseResults) parseResults.style.display = 'none';

        this.parsedData = null;
    }

    /**
     * 解析内容
     */
    async parseContent() {
        const contentInput = document.getElementById('contentInput');
        const content = contentInput.value.trim();

        if (!content) {
            this.showMessage('请先输入要解析的内容', 'warning');
            return;
        }

        this.showParsingStatus();

        try {
            // 调用AI解析内容
            const response = await chrome.runtime.sendMessage({
                action: 'callGeminiAI',
                prompt: this.buildContentParsePrompt(content),
                context: '你是一个专业的信息提取专家，专门从文本中提取结构化数据。'
            });

            if (response.success) {
                await this.processParseResult(response.data);
            } else {
                throw new Error('AI解析失败');
            }
        } catch (error) {
            console.error('内容解析失败:', error);
            this.hideParsingStatus();

            // 使用错误恢复管理器处理错误
            if (this.errorRecoveryManager) {
                this.errorRecoveryManager.handleError(error, {
                    operation: 'parseContent',
                    data: content,
                    retryFunction: () => this.parseContent()
                });
            } else {
                this.showMessage('内容解析失败: ' + error.message, 'error');
            }
        }
    }

    /**
     * 构建内容解析提示词
     */
    buildContentParsePrompt(content) {
        return `请从以下文本中提取MDAC表单所需的信息：

文本内容：
${content}

请提取以下字段（如果存在）：
- name: 姓名（英文全名）
- passportNo: 护照号码
- dateOfBirth: 出生日期（DD/MM/YYYY格式）
- nationality: 国籍（3位代码，如CHN、USA等）
- sex: 性别（1=男性，2=女性）
- passportExpiry: 护照到期日期（DD/MM/YYYY格式）
- email: 电子邮箱
- confirmEmail: 确认邮箱（与email相同）
- countryCode: 国家代码（如+86、+60等）
- mobileNo: 手机号码（不含国家代码）
- arrivalDate: 到达日期（DD/MM/YYYY格式）
- departureDate: 离开日期（DD/MM/YYYY格式）
- flightNo: 航班号或交通工具编号
- modeOfTravel: 旅行方式（AIR=航空，LAND=陆路，SEA=海路）
- lastPort: 最后港口（3位代码）
- accommodation: 住宿类型（01=酒店，02=朋友家，99=其他）
- address: 马来西亚地址行1（英文）
- address2: 马来西亚地址行2（英文，可选）
- state: 州代码（如14=吉隆坡）
- postcode: 邮政编码（5位数字）
- city: 城市代码

请只返回JSON格式的数据，不要包含其他说明。如果某个字段无法确定，请设为null。
对于中文信息，请自动翻译为英文。确保日期格式为DD/MM/YYYY。`;
    }

    /**
     * 显示解析状态
     */
    showParsingStatus() {
        const parsingStatus = document.getElementById('parsingStatus');
        const parseResults = document.getElementById('parseResults');
        const progressFill = document.getElementById('parseProgress');

        if (parsingStatus) {
            parsingStatus.style.display = 'block';

            // 模拟进度条动画
            let progress = 0;
            const progressInterval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress > 90) progress = 90;

                if (progressFill) {
                    progressFill.style.width = progress + '%';
                }

                if (progress >= 90) {
                    clearInterval(progressInterval);
                }
            }, 200);

            this.progressInterval = progressInterval;
        }

        if (parseResults) parseResults.style.display = 'none';
    }

    /**
     * 隐藏解析状态
     */
    hideParsingStatus() {
        const parsingStatus = document.getElementById('parsingStatus');
        if (parsingStatus) parsingStatus.style.display = 'none';

        if (this.progressInterval) {
            clearInterval(this.progressInterval);
            this.progressInterval = null;
        }
    }

    /**
     * 处理解析结果
     */
    async processParseResult(aiResponse) {
        this.hideParsingStatus();

        try {
            // 清理AI返回的JSON
            const cleanResult = aiResponse.replace(/```json|```/g, '').trim();
            const extractedData = JSON.parse(cleanResult);

            this.parsedData = extractedData;
            this.displayParseResults(extractedData);

        } catch (parseError) {
            console.error('解析AI返回的JSON失败:', parseError);
            this.showMessage('AI解析结果格式异常，请重试', 'error');
        }
    }

    /**
     * 显示解析结果
     */
    displayParseResults(data) {
        const parseResults = document.getElementById('parseResults');
        const extractedData = document.getElementById('extractedData');

        if (!parseResults || !extractedData) return;

        // 定义字段映射和必填字段
        const fieldLabels = {
            name: '姓名',
            passportNo: '护照号码',
            dateOfBirth: '出生日期',
            nationality: '国籍',
            sex: '性别',
            passportExpiry: '护照到期日期',
            email: '电子邮箱',
            confirmEmail: '确认邮箱',
            countryCode: '国家代码',
            mobileNo: '手机号码',
            arrivalDate: '到达日期',
            departureDate: '离开日期',
            flightNo: '航班号',
            modeOfTravel: '旅行方式',
            lastPort: '最后港口',
            accommodation: '住宿类型',
            address: '地址行1',
            address2: '地址行2',
            state: '州',
            postcode: '邮政编码',
            city: '城市'
        };

        const requiredFields = [
            'name', 'passportNo', 'dateOfBirth', 'nationality', 'sex',
            'passportExpiry', 'email', 'countryCode', 'mobileNo',
            'arrivalDate', 'departureDate', 'flightNo', 'modeOfTravel',
            'lastPort', 'accommodation', 'address', 'state', 'postcode', 'city'
        ];

        // 生成数据显示
        let dataHtml = '';
        let filledCount = 0;
        const missingFields = [];

        Object.keys(fieldLabels).forEach(fieldId => {
            const label = fieldLabels[fieldId];
            const value = data[fieldId];
            const hasValue = value && value.toString().trim() !== '';

            if (hasValue) filledCount++;
            else if (requiredFields.includes(fieldId)) {
                missingFields.push(label);
            }

            dataHtml += `
                <div class="data-item">
                    <span class="data-label">${label}:</span>
                    <span class="data-value ${hasValue ? '' : 'empty'}">${hasValue ? value : '未提取'}</span>
                </div>
            `;
        });

        extractedData.innerHTML = dataHtml;

        // 更新完整度指示器
        const completeness = Math.round((filledCount / Object.keys(fieldLabels).length) * 100);
        this.updateCompletenessIndicator(completeness);

        // 显示缺失字段
        this.displayMissingFields(missingFields);

        // 更新填充按钮状态
        const fillFormBtn = document.getElementById('fillFormBtn');
        const canFill = Object.keys(this.parsedData).length > 0;

        if (fillFormBtn) {
            fillFormBtn.disabled = !canFill;
            fillFormBtn.title = canFill ? '将解析的数据插入到MDAC表单中' : '没有可填充的数据';
        }

        parseResults.style.display = 'block';
        this.showMessage(`解析完成！提取了${filledCount}个字段`, 'success');
    }

    /**
     * 更新完整度指示器
     */
    updateCompletenessIndicator(percentage) {
        const completenessText = document.querySelector('.completeness-text');
        const completenessFill = document.getElementById('completenessFill');

        if (completenessText) {
            completenessText.textContent = `完整度: ${percentage}%`;
        }

        if (completenessFill) {
            completenessFill.style.width = percentage + '%';

            // 根据完整度改变颜色
            if (percentage >= 90) {
                completenessFill.style.background = 'linear-gradient(90deg, #28a745, #20c997)';
            } else if (percentage >= 70) {
                completenessFill.style.background = 'linear-gradient(90deg, #ffc107, #fd7e14)';
            } else {
                completenessFill.style.background = 'linear-gradient(90deg, #dc3545, #e74c3c)';
            }
        }
    }

    /**
     * 显示缺失字段
     */
    displayMissingFields(missingFields) {
        const missingFieldsDiv = document.getElementById('missingFields');
        const missingList = document.getElementById('missingList');

        if (!missingFieldsDiv || !missingList) return;

        if (missingFields.length > 0) {
            let missingHtml = '';
            missingFields.forEach(field => {
                missingHtml += `<span class="missing-item">${field}</span>`;
            });

            missingList.innerHTML = missingHtml;
            missingFieldsDiv.style.display = 'block';
        } else {
            missingFieldsDiv.style.display = 'none';
        }
    }

    /**
     * 编辑解析数据
     */
    editParsedData() {
        if (!this.parsedData) {
            this.showMessage('没有可编辑的数据', 'warning');
            return;
        }

        // 打开表单编辑器并预填数据
        chrome.tabs.create({
            url: chrome.runtime.getURL('form-editor.html') + '?data=' + encodeURIComponent(JSON.stringify(this.parsedData))
        });
    }

    /**
     * 复制解析数据到剪贴板
     */
    async copyParsedData() {
        if (!this.parsedData) {
            this.showMessage('没有可保存的数据', 'warning');
            return;
        }

        try {
            const dataText = JSON.stringify(this.parsedData, null, 2);
            await navigator.clipboard.writeText(dataText);
            this.showMessage('数据已复制到剪贴板', 'success');
        } catch (error) {
            console.error('复制数据失败:', error);
            this.showMessage('复制数据失败', 'error');
        }
    }

    /**
     * 显示合并数据预览
     */
    showMergedDataPreview(mergedData) {
        // 定义字段标签
        const fieldLabels = {
            name: '姓名',
            passportNo: '护照号码',
            dateOfBirth: '出生日期',
            nationality: '国籍',
            gender: '性别',
            passportExpiry: '护照到期日期',
            email: '邮箱地址',
            countryCode: '国家代码',
            mobileNo: '手机号码',
            arrivalDate: '到达日期',
            departureDate: '离开日期',
            flightNo: '航班号',
            modeOfTravel: '旅行方式',
            lastPort: '最后港口',
            accommodation: '住宿类型',
            address: '地址',
            state: '州/省',
            postcode: '邮编',
            city: '城市'
        };

        // 生成预览HTML
        let previewHtml = '<div class="merged-data-preview">';
        previewHtml += '<h4>📊 合并数据预览</h4>';

        // AI解析数据部分
        if (this.parsedData && Object.keys(this.parsedData).length > 0) {
            previewHtml += '<div class="data-source-section">';
            previewHtml += '<h5>🧠 AI解析数据（优先级高）</h5>';
            previewHtml += '<div class="data-fields">';

            Object.keys(this.parsedData).forEach(fieldKey => {
                const value = this.parsedData[fieldKey];
                const label = fieldLabels[fieldKey] || fieldKey;
                previewHtml += `
                    <div class="field-item ai-data">
                        <span class="field-label">${label}:</span>
                        <span class="field-value">${value}</span>
                    </div>
                `;
            });

            previewHtml += '</div></div>';
        }

        // 补充信息数据部分
        if (this.supplementData && this.supplementData.parsedFields && Object.keys(this.supplementData.parsedFields).length > 0) {
            previewHtml += '<div class="data-source-section">';
            previewHtml += '<h5>📝 补充信息数据</h5>';
            previewHtml += '<div class="data-fields">';

            Object.keys(this.supplementData.parsedFields).forEach(fieldKey => {
                // 如果AI数据中已有此字段，标记为被覆盖
                const isOverridden = this.parsedData && this.parsedData.hasOwnProperty(fieldKey);
                const value = this.supplementData.parsedFields[fieldKey];
                const label = fieldLabels[fieldKey] || fieldKey;

                previewHtml += `
                    <div class="field-item supplement-data ${isOverridden ? 'overridden' : ''}">
                        <span class="field-label">${label}:</span>
                        <span class="field-value">${value}</span>
                        ${isOverridden ? '<span class="override-note">（被AI数据覆盖）</span>' : ''}
                    </div>
                `;
            });

            previewHtml += '</div></div>';
        }

        // 最终合并结果
        previewHtml += '<div class="data-source-section final-result">';
        previewHtml += '<h5>🎯 最终合并结果</h5>';
        previewHtml += '<div class="data-fields">';

        Object.keys(mergedData).forEach(fieldKey => {
            const value = mergedData[fieldKey];
            const label = fieldLabels[fieldKey] || fieldKey;
            previewHtml += `
                <div class="field-item merged-data">
                    <span class="field-label">${label}:</span>
                    <span class="field-value">${value}</span>
                </div>
            `;
        });

        previewHtml += '</div></div>';
        previewHtml += '</div>';

        // 显示模态对话框
        this.showModal('数据合并预览', previewHtml, {
            confirmText: '确认填充表单',
            cancelText: '取消',
            onConfirm: () => {
                this.fillFormWithMergedData(mergedData);
            }
        });
    }

    /**
     * 使用合并数据填充表单
     */
    async fillFormWithMergedData(mergedData) {
        if (!mergedData || Object.keys(mergedData).length === 0) {
            this.showMessage('没有可填充的数据', 'warning');
            return;
        }

        if (!this.isMDACPage) {
            this.showMessage('请先打开MDAC网站页面', 'error');
            return;
        }

        try {
            this.aiStatus = 'working';
            this.updateAIStatus();
            this.showMessage('正在填充表单数据...', 'info');

            // 向content script发送填充指令
            await chrome.tabs.sendMessage(this.currentTab.id, {
                action: 'fillFormData',
                data: mergedData
            });

            this.aiStatus = 'ready';
            this.updateAIStatus();
            this.showMessage('✅ 表单数据填充完成！请检查并手动填写缺失的必填字段。', 'success');

        } catch (error) {
            console.error('表单填充失败:', error);
            this.aiStatus = 'error';
            this.updateAIStatus();
            this.showMessage('表单填充失败: ' + error.message, 'error');
        }
    }

    /**
     * 直接填充表单数据（更新为使用合并数据）
     */
    async fillFormWithParsedData() {
        // 合并数据源
        const mergedData = this.mergeDataSources();

        if (!mergedData || Object.keys(mergedData).length === 0) {
            this.showMessage('没有可填充的数据，请先进行AI解析或输入补充信息', 'warning');
            return;
        }

        if (!this.isMDACPage) {
            this.showMessage('请先打开MDAC网站页面', 'error');
            return;
        }

        // 如果有多个数据源，显示预览确认
        const hasAIData = this.parsedData && Object.keys(this.parsedData).length > 0;
        const hasSupplementData = this.supplementData && this.supplementData.parsedFields && Object.keys(this.supplementData.parsedFields).length > 0;

        if (hasAIData && hasSupplementData) {
            // 有多个数据源，显示预览
            this.showMergedDataPreview(mergedData);
        } else {
            // 只有一个数据源，直接填充
            await this.fillFormWithMergedData(mergedData);
        }
    }

    /**
     * 处理用户确认的数据
     */
    async handleConfirmedData(confirmedData) {
        try {
            // 更新解析数据
            this.parsedData = confirmedData;

            // 执行自动填充
            await this.autoFillFromParse();

            this.showMessage('数据确认完成，开始自动填充', 'success');
        } catch (error) {
            console.error('处理确认数据失败:', error);
            this.handleError(error, {
                operation: 'handleConfirmedData',
                data: confirmedData
            });
        }
    }

    /**
     * 统一错误处理方法
     */
    handleError(error, context = {}) {
        if (this.errorRecoveryManager) {
            this.errorRecoveryManager.handleError(error, context);
        } else {
            // 降级处理
            console.error('错误处理失败:', error);
            this.showMessage('操作失败: ' + error.message, 'error');
        }
    }

    /**
     * 从解析结果自动填充
     */
    async autoFillFromParse() {
        if (!this.parsedData) {
            this.showMessage('没有可填充的数据', 'warning');
            return;
        }

        if (!this.isMDACPage) {
            this.showMessage('请先打开MDAC网站', 'error');
            return;
        }

        try {
            this.aiStatus = 'working';
            this.updateAIStatus();

            // 向content script发送填充指令
            await chrome.tabs.sendMessage(this.currentTab.id, {
                action: 'fillFormData',
                data: this.parsedData
            });

            this.aiStatus = 'ready';
            this.updateAIStatus();
            this.showMessage('AI智能填充完成', 'success');

        } catch (error) {
            console.error('自动填充失败:', error);
            this.aiStatus = 'error';
            this.updateAIStatus();
            this.showMessage('自动填充失败: ' + error.message, 'error');
        }
    }
}

// 初始化弹窗
document.addEventListener('DOMContentLoaded', () => {
    new MDACPopup();
});
