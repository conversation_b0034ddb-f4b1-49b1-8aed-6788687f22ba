<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MDAC AI智能助手 - 侧边栏</title>
    <link rel="stylesheet" href="sidepanel.css">
</head>
<body class="sidepanel-body">
    <div class="sidepanel-container">
        <!-- 连接状态指示器 -->
        <div class="connection-status" id="connectionStatus">
            🟡 检测MDAC网站连接...
        </div>

        <!-- 头部区域 -->
        <div class="header">
            <div class="logo">
                <img src="icons/icon32.png" alt="MDAC AI">
                <h1>MDAC AI助手</h1>
            </div>
            <div class="ai-status" id="aiStatus">
                <span class="status-dot"></span>
                <span class="status-text">AI就绪</span>
            </div>
        </div>

        <!-- 网站检测区域 -->
        <div class="site-detection" id="siteDetection">
            <div class="detection-status" id="detectionStatus">
                <span class="icon">🔍</span>
                <span class="text">检测MDAC网站...</span>
            </div>
        </div>

        <!-- 主要功能区域 -->
        <div class="main-content" id="mainContent">
            <!-- 内容输入区域 - 直接可见 -->
            <div class="content-input-section">
                <div class="input-header">
                    <h3>🧠 AI智能内容解析</h3>
                    <div class="input-status" id="inputStatus">
                        <span class="status-text">支持文本和图片内容</span>
                    </div>
                </div>

                <div class="input-area">
                    <label for="contentInput">粘贴或输入任意格式的内容：</label>
                    <textarea
                        id="contentInput"
                        placeholder="粘贴邮件、文档、聊天记录等任意格式的内容，AI将自动提取表单所需信息...&#10;&#10;支持内容类型：&#10;• 护照信息截图文字&#10;• 酒店预订确认邮件&#10;• 航班信息&#10;• 个人资料文档&#10;• 图片中的文字信息（通过Gemini Vision API）"
                        rows="6">
                    </textarea>

                    <!-- 图片上传区域 -->
                    <div class="image-upload-area" id="imageUploadArea">
                        <input type="file" id="imageInput" accept="image/*" style="display: none;">
                        <button class="upload-btn" id="uploadImageBtn">
                            <span class="icon">📷</span>
                            <span class="text">上传图片</span>
                        </button>
                        <div class="upload-hint">支持JPG、PNG格式，AI将提取图片中的文字信息</div>
                    </div>

                    <div class="input-actions">
                        <button class="btn primary" id="parseContentBtn">
                            <span class="icon">🔍</span>
                            <span class="text">开始AI解析</span>
                        </button>
                        <button class="btn secondary" id="clearContentBtn">
                            <span class="icon">🗑️</span>
                            <span class="text">清空内容</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 补充信息输入区域 - 持久化数据 -->
            <div class="supplement-input-section">
                <div class="supplement-header">
                    <h3>📝 补充信息输入</h3>
                    <div class="supplement-status" id="supplementStatus">
                        <span class="status-text">暂无保存的补充信息</span>
                    </div>
                </div>

                <div class="supplement-area">
                    <label for="supplementInput">输入其他表单字段信息（自动保存）：</label>
                    <textarea
                        id="supplementInput"
                        placeholder="输入除个人基础信息外的其他MDAC表单字段内容：&#10;&#10;支持字段类型：&#10;• 联系信息：邮箱、电话号码、国家代码&#10;• 旅行信息：航班号、到达/离开日期、旅行方式&#10;• 住宿信息：住宿类型、地址、州、邮编、城市&#10;• 其他表单字段&#10;&#10;示例格式：&#10;邮箱：<EMAIL>&#10;电话：+60123456789&#10;航班号：MH123&#10;住宿地址：123 Main Street, Kuala Lumpur"
                        rows="5">
                    </textarea>

                    <div class="supplement-actions">
                        <button class="btn secondary" id="clearSupplementBtn">
                            <span class="icon">🗑️</span>
                            <span class="text">清空补充信息</span>
                        </button>
                        <button class="btn info" id="previewMergedDataBtn">
                            <span class="icon">👁️</span>
                            <span class="text">预览合并数据</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 快速操作 -->
            <div class="quick-actions">
                <button class="action-btn primary" id="quickFillBtn">
                    <span class="icon">🚀</span>
                    <span class="text">一键智能填充</span>
                </button>
                <button class="action-btn secondary" id="openFormBtn">
                    <span class="icon">📝</span>
                    <span class="text">打开表单编辑器</span>
                </button>
            </div>

            <!-- AI解析状态和结果区域 -->
            <div class="parsing-status" id="parsingStatus" style="display: none;">
                <div class="status-indicator">
                    <div class="loading-spinner"></div>
                    <span class="status-text">AI正在解析内容...</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="parseProgress"></div>
                </div>
            </div>

            <div class="parse-results" id="parseResults" style="display: none;">
                <div class="results-header">
                    <h4>📊 AI解析结果</h4>
                    <div class="completeness-indicator" id="completenessIndicator">
                        <span class="completeness-text">完整度: 0%</span>
                        <div class="completeness-bar">
                            <div class="completeness-fill" id="completenessFill"></div>
                        </div>
                    </div>
                </div>

                <div class="extracted-data" id="extractedData">
                    <!-- 动态生成的解析结果 -->
                </div>

                <div class="missing-fields" id="missingFields" style="display: none;">
                    <h5>⚠️ 缺失的必填字段</h5>
                    <div class="missing-list" id="missingList">
                        <!-- 动态生成的缺失字段列表 -->
                    </div>
                </div>

                <div class="results-actions">
                    <button class="btn secondary" id="editDataBtn">
                        <span class="icon">✏️</span>
                        <span class="text">编辑数据</span>
                    </button>
                    <button class="btn secondary" id="saveTemplateFromParseBtn">
                        <span class="icon">📋</span>
                        <span class="text">复制数据</span>
                    </button>
                    <button class="btn primary" id="fillFormBtn">
                        <span class="icon">📝</span>
                        <span class="text">插入表单</span>
                    </button>
                </div>
            </div>

            <!-- AI智能分析功能 -->
            <div class="ai-features">
                <h3>🤖 AI智能分析功能</h3>
                <div class="feature-list">
                    <div class="feature-item">
                        <span class="feature-icon">🧠</span>
                        <div class="feature-info">
                            <h4>智能内容解析</h4>
                            <p>从任意文本提取表单数据</p>
                        </div>
                        <div class="feature-status active"></div>
                    </div>
                    <div class="feature-item">
                        <span class="feature-icon">✅</span>
                        <div class="feature-info">
                            <h4>AI数据验证</h4>
                            <p>智能验证和优化建议</p>
                        </div>
                        <div class="feature-status active"></div>
                    </div>
                    <div class="feature-item">
                        <span class="feature-icon">🌐</span>
                        <div class="feature-info">
                            <h4>智能地址翻译</h4>
                            <p>中英文地址自动翻译</p>
                        </div>
                        <div class="feature-status active"></div>
                    </div>
                </div>
            </div>

            <!-- AI助手状态 -->
            <div class="ai-assistant" id="aiAssistant">
                <div class="ai-header">
                    <span class="ai-icon">🤖</span>
                    <span class="ai-title">AI助手</span>
                    <div class="ai-indicator" id="aiIndicator"></div>
                </div>
                <div class="ai-suggestions" id="aiSuggestions">
                    <p>准备就绪，等待您的指令...</p>
                </div>
            </div>
        </div>

        <!-- 设置和帮助 -->
        <div class="footer">
            <button class="footer-btn" id="aiSettingsBtn">
                <span class="icon">🤖</span>
                <span class="text">AI设置</span>
            </button>
            <button class="footer-btn" id="helpBtn">
                <span class="icon">❓</span>
                <span class="text">帮助</span>
            </button>
        </div>
    </div>

    <!-- 模态对话框 -->
    <div class="modal" id="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">标题</h3>
                <button class="modal-close" id="modalClose">&times;</button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- 动态内容 -->
            </div>
            <div class="modal-footer">
                <button class="btn secondary" id="modalCancel">取消</button>
                <button class="btn primary" id="modalConfirm">确认</button>
            </div>
        </div>
    </div>

    <script src="data-preview-manager.js"></script>
    <script src="error-recovery-manager.js"></script>
    <script src="fill-monitor.js"></script>
    <script src="sidepanel.js"></script>
</body>
</html>
