/**
 * MDAC AI智能填充工具 - 侧边栏版本
 * 处理扩展侧边栏的用户界面和交互逻辑
 */

class MDACAssistantSidePanel {
    constructor() {
        this.currentTab = null;
        this.isMDACPage = false;
        this.aiStatus = 'ready';
        this.parsedData = null;
        this.supplementData = null; // 持久化的补充信息
        this.mergedData = null; // 合并后的完整数据
        this.dataPreviewManager = null;
        this.errorRecoveryManager = null;
        this.fillMonitor = null;
        this.connectionCheckInterval = null;

        this.initializeSidePanel();
    }

    /**
     * 侧边栏特定初始化
     */
    async initializeSidePanel() {
        console.log('🚀 MDAC AI侧边栏正在初始化...');
        
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.init());
        } else {
            await this.init();
        }
        
        // 侧边栏特定的事件监听
        this.setupSidePanelEvents();
        
        // 开始定期检查连接状态
        this.startConnectionMonitoring();
    }

    /**
     * 初始化主要功能
     */
    async init() {
        await this.getCurrentTab();
        await this.detectMDACPage();
        this.setupEventListeners();
        await this.loadUserSettings();
        this.initializeDataPreviewManager();
        this.initializeErrorRecoveryManager();
        this.initializeFillMonitor();
        this.initializeSupplementInput();
        this.updateUI();
        await this.testAIConnection();
    }

    /**
     * 设置侧边栏特定事件
     */
    setupSidePanelEvents() {
        // 监听窗口大小变化
        window.addEventListener('resize', () => this.handleResize());
        
        // 监听页面可见性变化
        document.addEventListener('visibilitychange', () => this.handleVisibilityChange());
        
        // 监听侧边栏关闭事件
        window.addEventListener('beforeunload', () => this.handleSidePanelClose());
        
        // 监听标签页切换
        chrome.tabs.onActivated.addListener(() => this.handleTabChange());
        chrome.tabs.onUpdated.addListener(() => this.handleTabChange());
    }

    /**
     * 开始连接监控
     */
    startConnectionMonitoring() {
        // 立即检查一次
        this.checkPageConnection();
        
        // 每5秒检查一次连接状态
        this.connectionCheckInterval = setInterval(() => {
            this.checkPageConnection();
        }, 5000);
    }

    /**
     * 停止连接监控
     */
    stopConnectionMonitoring() {
        if (this.connectionCheckInterval) {
            clearInterval(this.connectionCheckInterval);
            this.connectionCheckInterval = null;
        }
    }

    /**
     * 处理窗口大小变化
     */
    handleResize() {
        // 调整布局以适应新的窗口大小
        this.adjustLayout();
    }

    /**
     * 处理可见性变化
     */
    handleVisibilityChange() {
        if (document.hidden) {
            console.log('📱 侧边栏已隐藏');
            this.stopConnectionMonitoring();
        } else {
            console.log('📱 侧边栏已显示');
            // 重新开始监控
            this.startConnectionMonitoring();
        }
    }

    /**
     * 处理侧边栏关闭
     */
    handleSidePanelClose() {
        console.log('📱 侧边栏正在关闭');
        // 保存当前状态
        this.saveCurrentState();
        // 停止监控
        this.stopConnectionMonitoring();
    }

    /**
     * 处理标签页变化
     */
    async handleTabChange() {
        await this.getCurrentTab();
        await this.detectMDACPage();
        this.checkPageConnection();
    }

    /**
     * 调整布局
     */
    adjustLayout() {
        const container = document.querySelector('.sidepanel-container');
        if (container) {
            const width = window.innerWidth;
            if (width < 350) {
                container.classList.add('compact-mode');
            } else {
                container.classList.remove('compact-mode');
            }
        }
    }

    /**
     * 检查与页面的连接状态
     */
    async checkPageConnection() {
        try {
            const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
            const activeTab = tabs[0];
            
            if (activeTab && activeTab.url && activeTab.url.includes('imigresen-online.imi.gov.my')) {
                this.updateConnectionStatus('connected');
                this.isMDACPage = true;
            } else {
                this.updateConnectionStatus('disconnected');
                this.isMDACPage = false;
            }
            
            // 更新UI状态
            this.updateUI();
        } catch (error) {
            console.error('检查页面连接失败:', error);
            this.updateConnectionStatus('error');
        }
    }

    /**
     * 更新连接状态显示
     */
    updateConnectionStatus(status) {
        const statusElement = document.querySelector('#connectionStatus');
        if (statusElement) {
            statusElement.className = `connection-status ${status}`;
            statusElement.textContent = this.getConnectionStatusText(status);
        }
    }

    /**
     * 获取连接状态文本
     */
    getConnectionStatusText(status) {
        const statusTexts = {
            connected: '🟢 已连接到MDAC网站',
            disconnected: '🟡 请打开MDAC网站使用完整功能',
            error: '🔴 连接检测异常'
        };
        return statusTexts[status] || '🟡 状态未知';
    }

    /**
     * 保存当前状态
     */
    saveCurrentState() {
        try {
            const state = {
                parsedData: this.parsedData,
                supplementData: this.supplementData,
                mergedData: this.mergedData,
                timestamp: Date.now()
            };
            
            chrome.storage.local.set({ 'mdac_sidepanel_state': state }, () => {
                console.log('侧边栏状态已保存');
            });
        } catch (error) {
            console.error('保存状态失败:', error);
        }
    }

    /**
     * 恢复保存的状态
     */
    async restoreSavedState() {
        try {
            const result = await chrome.storage.local.get(['mdac_sidepanel_state']);
            const state = result.mdac_sidepanel_state;
            
            if (state && state.timestamp) {
                // 检查状态是否过期（24小时）
                const now = Date.now();
                const maxAge = 24 * 60 * 60 * 1000; // 24小时
                
                if (now - state.timestamp < maxAge) {
                    this.parsedData = state.parsedData;
                    this.supplementData = state.supplementData;
                    this.mergedData = state.mergedData;
                    console.log('已恢复侧边栏状态');
                    return true;
                }
            }
        } catch (error) {
            console.error('恢复状态失败:', error);
        }
        return false;
    }

    /**
     * 获取当前标签页
     */
    async getCurrentTab() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            this.currentTab = tab;
        } catch (error) {
            console.error('获取当前标签页失败:', error);
        }
    }

    /**
     * 检测是否为MDAC页面
     */
    async detectMDACPage() {
        if (!this.currentTab || !this.currentTab.url) {
            this.isMDACPage = false;
            return;
        }

        this.isMDACPage = this.currentTab.url.includes('imigresen-online.imi.gov.my');
        
        if (this.isMDACPage) {
            console.log('✅ 检测到MDAC网站');
            this.updateDetectionStatus('detected', '✅ 已检测到MDAC网站');
        } else {
            console.log('⚠️ 当前不在MDAC网站');
            this.updateDetectionStatus('not-detected', '⚠️ 请打开MDAC网站');
        }
    }

    /**
     * 更新检测状态显示
     */
    updateDetectionStatus(status, message) {
        const detectionElement = document.querySelector('#detectionStatus');
        if (detectionElement) {
            const textElement = detectionElement.querySelector('.text');
            if (textElement) {
                textElement.textContent = message;
            }
            
            detectionElement.className = `detection-status ${status}`;
        }
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // AI解析相关
        document.getElementById('parseContentBtn')?.addEventListener('click', () => this.parseContent());
        document.getElementById('clearContentBtn')?.addEventListener('click', () => this.clearContent());
        
        // 图片上传
        document.getElementById('uploadImageBtn')?.addEventListener('click', () => this.triggerImageUpload());
        document.getElementById('imageInput')?.addEventListener('change', (e) => this.handleImageUpload(e));
        
        // 补充信息相关
        document.getElementById('supplementInput')?.addEventListener('input', () => this.handleSupplementInput());
        document.getElementById('clearSupplementBtn')?.addEventListener('click', () => this.clearSupplementData());
        document.getElementById('previewMergedDataBtn')?.addEventListener('click', () => this.previewMergedData());
        
        // 表单填充
        document.getElementById('fillFormBtn')?.addEventListener('click', () => this.fillForm());
        document.getElementById('quickFillBtn')?.addEventListener('click', () => this.quickFill());
        
        // 其他功能
        document.getElementById('openFormBtn')?.addEventListener('click', () => this.openFormEditor());
        document.getElementById('aiSettingsBtn')?.addEventListener('click', () => this.openAISettings());
        document.getElementById('helpBtn')?.addEventListener('click', () => this.showHelp());
        
        // 模态框
        document.getElementById('modalClose')?.addEventListener('click', () => this.closeModal());
        document.getElementById('modalCancel')?.addEventListener('click', () => this.closeModal());
        document.getElementById('modalConfirm')?.addEventListener('click', () => this.handleModalConfirm());
    }

    /**
     * 加载用户设置
     */
    async loadUserSettings() {
        try {
            const result = await chrome.storage.sync.get(['mdacSettings']);
            this.userSettings = result.mdacSettings || {};
            console.log('用户设置已加载');
        } catch (error) {
            console.error('加载用户设置失败:', error);
            this.userSettings = {};
        }
    }

    /**
     * 初始化数据预览管理器
     */
    initializeDataPreviewManager() {
        try {
            this.dataPreviewManager = new DataPreviewManager();
            console.log('数据预览管理器初始化成功');
        } catch (error) {
            console.error('数据预览管理器初始化失败:', error);
        }
    }

    /**
     * 初始化错误恢复管理器
     */
    initializeErrorRecoveryManager() {
        try {
            this.errorRecoveryManager = new ErrorRecoveryManager();
            console.log('错误恢复管理器初始化成功');
        } catch (error) {
            console.error('错误恢复管理器初始化失败:', error);
        }
    }

    /**
     * 初始化填充监控器
     */
    initializeFillMonitor() {
        try {
            this.fillMonitor = new FillMonitor();
            console.log('填充监控器初始化成功');
        } catch (error) {
            console.error('填充监控器初始化失败:', error);
        }
    }

    /**
     * 初始化补充信息输入
     */
    async initializeSupplementInput() {
        try {
            // 加载保存的补充信息
            const result = await chrome.storage.local.get(['mdac_supplement_data']);
            if (result.mdac_supplement_data) {
                this.supplementData = result.mdac_supplement_data;
                
                // 填充到输入框
                const supplementInput = document.getElementById('supplementInput');
                if (supplementInput && this.supplementData.rawText) {
                    supplementInput.value = this.supplementData.rawText;
                }
                
                // 更新状态显示
                this.updateSupplementStatus();
            }
            
            console.log('补充信息输入初始化成功');
        } catch (error) {
            console.error('补充信息输入初始化失败:', error);
        }
    }

    /**
     * 更新UI状态
     */
    updateUI() {
        // 更新AI状态
        this.updateAIStatus();
        
        // 更新按钮状态
        this.updateButtonStates();
        
        // 更新功能可用性
        this.updateFeatureAvailability();
    }

    /**
     * 更新AI状态显示
     */
    updateAIStatus() {
        const statusElement = document.querySelector('#aiStatus');
        if (statusElement) {
            const statusDot = statusElement.querySelector('.status-dot');
            const statusText = statusElement.querySelector('.status-text');
            
            if (statusDot && statusText) {
                switch (this.aiStatus) {
                    case 'ready':
                        statusDot.style.background = '#4caf50';
                        statusText.textContent = 'AI就绪';
                        break;
                    case 'working':
                        statusDot.style.background = '#ff9800';
                        statusText.textContent = 'AI工作中';
                        break;
                    case 'error':
                        statusDot.style.background = '#f44336';
                        statusText.textContent = 'AI异常';
                        break;
                    default:
                        statusDot.style.background = '#9e9e9e';
                        statusText.textContent = '状态未知';
                }
            }
        }
    }

    /**
     * 更新按钮状态
     */
    updateButtonStates() {
        const parseBtn = document.getElementById('parseContentBtn');
        const fillBtn = document.getElementById('fillFormBtn');
        const quickFillBtn = document.getElementById('quickFillBtn');
        
        // 根据页面状态和数据状态更新按钮
        if (parseBtn) {
            parseBtn.disabled = this.aiStatus === 'working';
        }
        
        if (fillBtn) {
            fillBtn.disabled = !this.isMDACPage || (!this.parsedData && !this.supplementData);
        }
        
        if (quickFillBtn) {
            quickFillBtn.disabled = !this.isMDACPage || (!this.parsedData && !this.supplementData);
        }
    }

    /**
     * 更新功能可用性
     */
    updateFeatureAvailability() {
        const mainContent = document.querySelector('#mainContent');
        if (mainContent) {
            if (this.isMDACPage) {
                mainContent.classList.remove('disabled');
            } else {
                // 不完全禁用，但显示提示
                const quickActions = document.querySelector('.quick-actions');
                if (quickActions) {
                    const buttons = quickActions.querySelectorAll('button');
                    buttons.forEach(btn => {
                        if (btn.id === 'quickFillBtn') {
                            btn.disabled = true;
                            btn.title = '请先打开MDAC网站';
                        }
                    });
                }
            }
        }
    }

    /**
     * 测试AI连接
     */
    async testAIConnection() {
        this.aiStatus = 'working';
        this.updateAIStatus();

        try {
            const response = await chrome.runtime.sendMessage({
                action: 'test-ai-connection'
            });

            if (response && response.success) {
                this.aiStatus = 'ready';
                console.log('✅ AI连接测试成功');
            } else {
                throw new Error(response?.error || 'AI连接测试失败');
            }
        } catch (error) {
            this.aiStatus = 'error';
            console.error('❌ AI连接测试失败:', error);
        }

        this.updateAIStatus();
    }

    /**
     * 解析内容
     */
    async parseContent() {
        const contentInput = document.getElementById('contentInput');
        const content = contentInput?.value?.trim();

        if (!content) {
            this.showMessage('请先输入要解析的内容', 'warning');
            return;
        }

        this.showParsingStatus(true);
        this.aiStatus = 'working';
        this.updateAIStatus();

        try {
            const response = await chrome.runtime.sendMessage({
                action: 'callGeminiAI',
                prompt: `请从以下内容中提取MDAC表单所需的个人信息：\n\n${content}`,
                context: 'MDAC表单填充'
            });

            if (response && response.success) {
                this.parsedData = this.parseAIResponse(response.data);
                this.displayParseResults();
                this.showMessage('内容解析完成', 'success');
            } else {
                throw new Error(response?.error || 'AI解析失败');
            }
        } catch (error) {
            console.error('内容解析失败:', error);
            this.showMessage('内容解析失败: ' + error.message, 'error');
        }

        this.showParsingStatus(false);
        this.aiStatus = 'ready';
        this.updateAIStatus();
    }

    /**
     * 处理图片上传
     */
    triggerImageUpload() {
        document.getElementById('imageInput')?.click();
    }

    /**
     * 处理图片上传事件
     */
    async handleImageUpload(event) {
        const file = event.target.files[0];
        if (!file) return;

        if (!file.type.startsWith('image/')) {
            this.showMessage('请选择有效的图片文件', 'error');
            return;
        }

        this.showParsingStatus(true);
        this.aiStatus = 'working';
        this.updateAIStatus();

        try {
            const base64 = await this.fileToBase64(file);
            const response = await chrome.runtime.sendMessage({
                action: 'callGeminiVision',
                image: base64,
                prompt: '请提取图片中的文字信息，特别是个人身份信息、护照信息、联系方式等'
            });

            if (response && response.success) {
                // 将提取的文字填入内容输入框
                const contentInput = document.getElementById('contentInput');
                if (contentInput) {
                    contentInput.value = response.data;
                }
                this.showMessage('图片文字提取成功', 'success');
            } else {
                throw new Error(response?.error || '图片文字提取失败');
            }
        } catch (error) {
            console.error('图片处理失败:', error);
            this.showMessage('图片处理失败: ' + error.message, 'error');
        }

        this.showParsingStatus(false);
        this.aiStatus = 'ready';
        this.updateAIStatus();
    }

    /**
     * 文件转Base64
     */
    fileToBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => {
                const base64 = reader.result.split(',')[1];
                resolve(base64);
            };
            reader.onerror = reject;
            reader.readAsDataURL(file);
        });
    }

    /**
     * 处理补充信息输入
     */
    async handleSupplementInput() {
        const supplementInput = document.getElementById('supplementInput');
        const rawText = supplementInput?.value || '';

        // 解析补充信息
        const parsedSupplement = this.parseSupplementText(rawText);

        // 保存到本地存储
        this.supplementData = {
            rawText: rawText,
            parsedFields: parsedSupplement,
            timestamp: Date.now()
        };

        try {
            await chrome.storage.local.set({ 'mdac_supplement_data': this.supplementData });
            this.updateSupplementStatus();
        } catch (error) {
            console.error('保存补充信息失败:', error);
        }
    }

    /**
     * 解析补充信息文本
     */
    parseSupplementText(text) {
        const fields = {};
        const lines = text.split('\n');

        const fieldMappings = {
            '邮箱': 'email',
            '电话': 'mobileNo',
            '国家代码': 'countryCode',
            '航班号': 'flightNo',
            '到达日期': 'arrivalDate',
            '离开日期': 'departureDate',
            '住宿类型': 'accommodation',
            '地址': 'address',
            '州': 'state',
            '邮编': 'postcode',
            '城市': 'city',
            '旅行方式': 'modeOfTravel',
            '最后港口': 'lastPort'
        };

        lines.forEach(line => {
            const colonIndex = line.indexOf('：');
            if (colonIndex > 0) {
                const key = line.substring(0, colonIndex).trim();
                const value = line.substring(colonIndex + 1).trim();

                if (fieldMappings[key] && value) {
                    fields[fieldMappings[key]] = value;
                }
            }
        });

        return fields;
    }

    /**
     * 更新补充信息状态
     */
    updateSupplementStatus() {
        const statusElement = document.querySelector('#supplementStatus .status-text');
        if (statusElement && this.supplementData) {
            const fieldCount = Object.keys(this.supplementData.parsedFields || {}).length;
            const charCount = (this.supplementData.rawText || '').length;

            if (fieldCount > 0) {
                statusElement.textContent = `已保存${fieldCount}个字段，${charCount}个字符`;
            } else if (charCount > 0) {
                statusElement.textContent = `已保存${charCount}个字符，等待解析`;
            } else {
                statusElement.textContent = '暂无保存的补充信息';
            }
        }
    }

    /**
     * 清空补充信息
     */
    async clearSupplementData() {
        try {
            await chrome.storage.local.remove(['mdac_supplement_data']);
            this.supplementData = null;

            const supplementInput = document.getElementById('supplementInput');
            if (supplementInput) {
                supplementInput.value = '';
            }

            this.updateSupplementStatus();
            this.showMessage('补充信息已清空', 'success');
        } catch (error) {
            console.error('清空补充信息失败:', error);
            this.showMessage('清空失败', 'error');
        }
    }

    /**
     * 预览合并数据
     */
    previewMergedData() {
        if (!this.parsedData && !this.supplementData) {
            this.showMessage('暂无数据可预览', 'warning');
            return;
        }

        this.mergedData = this.mergeDataSources();

        if (this.dataPreviewManager) {
            this.dataPreviewManager.showMergedDataPreview(
                this.parsedData,
                this.supplementData?.parsedFields,
                this.mergedData
            );
        }
    }

    /**
     * 合并数据源
     */
    mergeDataSources() {
        const merged = {};

        // 先添加补充信息数据
        if (this.supplementData && this.supplementData.parsedFields) {
            Object.assign(merged, this.supplementData.parsedFields);
        }

        // AI解析数据优先级更高，会覆盖补充信息中的同名字段
        if (this.parsedData) {
            Object.assign(merged, this.parsedData);
        }

        return merged;
    }

    /**
     * 填充表单
     */
    async fillForm() {
        if (!this.isMDACPage) {
            this.showMessage('请先打开MDAC网站', 'warning');
            return;
        }

        // 如果有多个数据源，显示预览
        if (this.parsedData && this.supplementData) {
            this.previewMergedData();
            return;
        }

        // 单一数据源直接填充
        const dataToFill = this.parsedData || this.supplementData?.parsedFields;
        if (!dataToFill) {
            this.showMessage('暂无数据可填充', 'warning');
            return;
        }

        await this.performFormFill(dataToFill);
    }

    /**
     * 执行表单填充
     */
    async performFormFill(data) {
        try {
            const response = await chrome.tabs.sendMessage(this.currentTab.id, {
                action: 'fillForm',
                data: data
            });

            if (response && response.success) {
                this.showMessage('表单填充成功', 'success');
                if (this.fillMonitor) {
                    this.fillMonitor.startMonitoring();
                }
            } else {
                throw new Error(response?.error || '表单填充失败');
            }
        } catch (error) {
            console.error('表单填充失败:', error);
            this.showMessage('表单填充失败: ' + error.message, 'error');
        }
    }

    /**
     * 一键智能填充
     */
    async quickFill() {
        if (!this.isMDACPage) {
            this.showMessage('请先打开MDAC网站', 'warning');
            return;
        }

        // 合并所有可用数据
        const mergedData = this.mergeDataSources();
        if (Object.keys(mergedData).length === 0) {
            this.showMessage('暂无数据可填充', 'warning');
            return;
        }

        await this.performFormFill(mergedData);
    }

    /**
     * 显示消息
     */
    showMessage(message, type = 'info') {
        // 创建消息提示
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;
        messageDiv.textContent = message;
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 16px;
            border-radius: 4px;
            color: white;
            font-size: 13px;
            z-index: 10000;
            max-width: 300px;
            word-wrap: break-word;
        `;

        // 设置背景色
        switch (type) {
            case 'success':
                messageDiv.style.background = '#4caf50';
                break;
            case 'error':
                messageDiv.style.background = '#f44336';
                break;
            case 'warning':
                messageDiv.style.background = '#ff9800';
                break;
            default:
                messageDiv.style.background = '#2196f3';
        }

        document.body.appendChild(messageDiv);

        // 3秒后自动移除
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 3000);
    }

    /**
     * 显示解析状态
     */
    showParsingStatus(show) {
        const statusElement = document.getElementById('parsingStatus');
        if (statusElement) {
            statusElement.style.display = show ? 'block' : 'none';
        }
    }

    /**
     * 清空内容
     */
    clearContent() {
        const contentInput = document.getElementById('contentInput');
        if (contentInput) {
            contentInput.value = '';
        }

        this.parsedData = null;
        this.hideParseResults();
        this.showMessage('内容已清空', 'success');
    }

    /**
     * 隐藏解析结果
     */
    hideParseResults() {
        const resultsElement = document.getElementById('parseResults');
        if (resultsElement) {
            resultsElement.style.display = 'none';
        }
    }

    /**
     * 解析AI响应
     */
    parseAIResponse(response) {
        // 这里应该包含解析AI响应的逻辑
        // 简化版本，实际应该更复杂
        try {
            if (typeof response === 'string') {
                // 尝试从文本中提取结构化数据
                return this.extractStructuredData(response);
            }
            return response;
        } catch (error) {
            console.error('解析AI响应失败:', error);
            return {};
        }
    }

    /**
     * 从文本中提取结构化数据
     */
    extractStructuredData(text) {
        const data = {};
        // 简化的提取逻辑
        // 实际应该更复杂和准确
        return data;
    }

    /**
     * 显示解析结果
     */
    displayParseResults() {
        const resultsElement = document.getElementById('parseResults');
        if (resultsElement && this.parsedData) {
            resultsElement.style.display = 'block';
            // 这里应该显示解析结果的详细内容
        }
    }

    /**
     * 打开表单编辑器
     */
    openFormEditor() {
        chrome.tabs.create({ url: 'form-editor.html' });
    }

    /**
     * 打开AI设置
     */
    openAISettings() {
        chrome.runtime.openOptionsPage();
    }

    /**
     * 显示帮助
     */
    showHelp() {
        this.showModal('帮助信息', '这里是MDAC AI助手的使用说明...');
    }

    /**
     * 显示模态框
     */
    showModal(title, content) {
        const modal = document.getElementById('modal');
        const modalTitle = document.getElementById('modalTitle');
        const modalBody = document.getElementById('modalBody');

        if (modal && modalTitle && modalBody) {
            modalTitle.textContent = title;
            modalBody.innerHTML = content;
            modal.classList.add('show');
        }
    }

    /**
     * 关闭模态框
     */
    closeModal() {
        const modal = document.getElementById('modal');
        if (modal) {
            modal.classList.remove('show');
        }
    }

    /**
     * 处理模态框确认
     */
    handleModalConfirm() {
        // 处理确认操作
        this.closeModal();
    }
}

// 初始化侧边栏
const mdacSidePanel = new MDACAssistantSidePanel();
