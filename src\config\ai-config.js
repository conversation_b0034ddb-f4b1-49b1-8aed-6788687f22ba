/**
 * MDAC AI智能填充工具 - AI配置文件
 * 从原始HTML文件提取的Gemini AI配置和功能
 */

// Gemini AI配置（从原文件提取）
var GEMINI_CONFIG = {
    // 默认API密钥（从原文件提取）
    DEFAULT_API_KEY: 'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s',
    
    // 默认模型
    DEFAULT_MODEL: 'gemini-2.5-flash-lite-preview-06-17',
    
    // API基础URL
    API_BASE_URL: 'https://generativelanguage.googleapis.com/v1beta/models',
    
    // 默认生成配置
    DEFAULT_GENERATION_CONFIG: {
        temperature: 0.,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 1024,
        candidateCount: 1
    },
    
    // 安全设置
    SAFETY_SETTINGS: [
        {
            category: "HARM_CATEGORY_HARASSMENT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
        },
        {
            category: "HARM_CATEGORY_HATE_SPEECH",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
        },
        {
            category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
        },
        {
            category: "HARM_CATEGORY_DANGEROUS_CONTENT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
        }
    ]
};

// AI提示词模板（从原文件提取）
var AI_PROMPTS = {
    // 字段验证提示词
    FIELD_VALIDATION: {
        name: (value) => `请验证"${value}"是否是有效的英文姓名格式，适用于护照和官方文件。`,
        passportNo: (value) => `请验证"${value}"是否是有效的护照号码格式。`,
        email: (value) => `请验证"${value}"是否是有效的电子邮箱地址格式。`,
        mobileNo: (value) => `请验证"${value}"是否是有效的手机号码格式。`,
        postcode: (value) => `请验证"${value}"是否是马来西亚的有效邮政编码。`,
        address: (value) => `请验证"${value}"是否是合适的马来西亚地址格式。`,
        city: (value) => `确认"${value}"是否是马来西亚的有效城市名称，如果不是请提供建议。`
    },
    
    // 表单优化提示词
    FORM_OPTIMIZATION: `请全面分析以下MDAC表单数据，检查：

表单数据：
{formData}

请检查：
1. 数据格式是否正确
2. 必填字段是否完整
3. 逻辑关系是否合理（如到达日期应早于离开日期）
4. 完整性
5. 提供改进建议

请保持建议实用且简洁。`,

    // 个人信息解析提示词
    PERSONAL_INFO_PARSING: `请从以下文本中提取MDAC表单的个人信息字段：

文本内容：
{content}

请提取以下个人信息字段（如果存在）：
- name: 姓名（英文全名，如果是中文请翻译为标准英文姓名格式）
- passportNo: 护照号码（字母+数字组合，如A12345678）
- dateOfBirth: 出生日期（严格DD/MM/YYYY格式）
- nationality: 国籍（3位ISO代码，如CHN=中国，USA=美国，SGP=新加坡，MYS=马来西亚）
- sex: 性别（1=男性，2=女性）
- passportExpiry: 护照到期日期（严格DD/MM/YYYY格式）
- email: 电子邮箱（标准邮箱格式）
- confirmEmail: 确认邮箱（与email相同）
- countryCode: 国家代码（如+86=中国，+60=马来西亚，+1=美国）
- mobileNo: 手机号码（纯数字，不含国家代码）

智能识别规则：
1. 姓名：优先识别护照上的英文姓名，中文姓名需翻译为标准拼音格式
2. 护照号：通常为1-2个字母****位数字的组合
3. 日期：自动识别各种日期格式并转换为DD/MM/YYYY
4. 国籍：根据护照签发国或明确提及的国籍信息判断
5. 性别：从文本中的性别词汇、称谓或护照信息中推断
6. 联系方式：提取有效的邮箱和电话号码

重要要求：
1. 只返回JSON格式，不包含任何说明文字或markdown标记
2. 无法确定的字段设为null
3. 中文信息必须翻译为英文
4. 日期格式必须严格为DD/MM/YYYY
5. 确保数据适合官方表单填写`,

    // 旅行信息解析提示词
    TRAVEL_INFO_PARSING: `请从以下文本中提取MDAC表单的旅行信息字段：

文本内容：
{content}

请提取以下旅行信息字段（如果存在）：
- arrivalDate: 到达马来西亚日期（严格DD/MM/YYYY格式）
- departureDate: 离开马来西亚日期（严格DD/MM/YYYY格式）
- flightNo: 航班号或交通工具编号（如MH123、CZ351）
- modeOfTravel: 旅行方式（AIR=航空，LAND=陆路，SEA=海路）
- lastPort: 最后出发港口（3位代码，如PEK=北京，PVG=上海，SIN=新加坡）
- accommodation: 住宿类型（01=酒店，02=朋友家，03=民宿，99=其他）
- address: 马来西亚住宿地址行1（英文）
- address2: 马来西亚住宿地址行2（英文，可选）
- state: 州代码（14=吉隆坡，01=柔佛，07=槟城，04=雪兰莪等）
- postcode: 邮政编码（5位数字）
- city: 城市代码（1400=吉隆坡，1000=槟城，0700=马六甲等）

智能识别规则：
1. 日期：从机票、酒店预订或行程安排中提取到达和离开日期
2. 航班：识别航空公司代码+数字的航班号格式
3. 交通方式：根据航班号、火车票、船票等信息判断
4. 住宿：从酒店预订、地址信息中判断住宿类型和地址
5. 地理位置：根据酒店名称、地址自动匹配对应的州和城市代码
6. 邮政编码：从完整地址中提取5位数字邮编

马来西亚主要城市代码参考：
- 吉隆坡：州代码14，城市代码1400
- 槟城：州代码07，城市代码1000
- 马六甲：州代码04，城市代码0700
- 新山：州代码01，城市代码0100

重要要求：
1. 只返回JSON格式，不包含任何说明文字或markdown标记
2. 无法确定的字段设为null
3. 地址必须翻译为英文且符合马来西亚地址格式
4. 日期格式必须严格为DD/MM/YYYY
5. 确保到达日期早于离开日期的逻辑合理性`,

    // 通用内容解析提示词（保留向后兼容）
    CONTENT_PARSING: `请从以下文本中提取MDAC表单所需的信息：

文本内容：
{content}

请提取以下字段（如果存在）：
- name: 姓名（英文全名，如果是中文请翻译）
- passportNo: 护照号码
- dateOfBirth: 出生日期（DD/MM/YYYY格式）
- nationality: 国籍（3位代码，如CHN、USA、SGP等）
- sex: 性别（1=男性，2=女性）
- passportExpiry: 护照到期日期（DD/MM/YYYY格式）
- email: 电子邮箱
- confirmEmail: 确认邮箱（与email相同）
- countryCode: 国家代码（如+86、+60、+1等）
- mobileNo: 手机号码（不含国家代码）
- arrivalDate: 到达日期（DD/MM/YYYY格式）
- departureDate: 离开日期（DD/MM/YYYY格式）
- flightNo: 航班号或交通工具编号
- modeOfTravel: 旅行方式（AIR=航空，LAND=陆路，SEA=海路）
- lastPort: 最后港口（3位代码）
- accommodation: 住宿类型（01=酒店，02=朋友家，99=其他）
- address: 马来西亚地址行1（英文）
- address2: 马来西亚地址行2（英文，可选）
- state: 州代码（如14=吉隆坡，01=柔佛等）
- postcode: 邮政编码（5位数字）
- city: 城市代码

重要要求：
1. 请只返回JSON格式的数据，不要包含其他说明或markdown标记
2. 如果某个字段无法确定，请设为null
3. 对于中文信息，请自动翻译为英文
4. 确保日期格式严格为DD/MM/YYYY
5. 国籍和港口代码使用标准的3位ISO代码
6. 性别用数字：1=男性，2=女性
7. 地址必须翻译为英文且适合官方表单填写`,

    // 地址翻译提示词
    ADDRESS_TRANSLATION: `请将以下中文地址翻译为英文，适用于马来西亚入境卡填写：

中文地址：{address}

翻译要求：
1. 保持地址的准确性和完整性
2. 使用标准的英文地址格式
3. 确保只包含字母、数字、空格、逗号和句号
4. 适合官方表单填写

请只返回翻译后的英文地址，不要包含其他说明。`,

    // API分析提示词
    API_ANALYSIS: `请分析以下MDAC API请求结构，提供技术分析和实现建议：

API请求数据：
{requests}

请分析：
1. API端点的功能和用途
2. 请求参数的格式和要求
3. 安全机制（CSRF、认证等）
4. 实现直接提交的技术方案
5. 潜在的技术风险和挑战`,

    // 连接测试提示词
    CONNECTION_TEST: '请回复"AI连接正常"',

    // Google Maps地址标准化提示词
    GOOGLE_MAPS_ADDRESS_STANDARDIZATION: `请协助Google Maps API进行地址标准化分析：

输入地址：{address}
Google Maps结果：{mapsResult}

请分析并提供：
1. 地址标准化建议
2. MDAC表单字段映射
3. 数据质量评估
4. 改进建议

输出格式：
{
  "standardizedAddress": "标准化地址",
  "mdacMapping": {
    "address": "地址行1",
    "address2": "地址行2",
    "state": "州代码",
    "city": "城市代码",
    "postcode": "邮政编码"
  },
  "confidence": 0.95,
  "suggestions": ["改进建议"]
}`,

    // 地址验证提示词
    ADDRESS_VALIDATION: `请验证以下地址是否符合MDAC要求：

地址信息：{addressData}

验证要求：
1. 地址必须在马来西亚境内
2. 格式必须为英文
3. 州-城市-邮编必须匹配
4. 地址行1必须包含街道信息
5. 邮政编码必须为5位数字

输出格式：
{
  "isValid": true/false,
  "errors": ["错误列表"],
  "warnings": ["警告列表"],
  "suggestions": ["改进建议"]
}`
};

// AI上下文模板 - 优化版
var AI_CONTEXTS = {
    FORM_VALIDATOR: 'MDAC表单验证专家，具备完整的马来西亚入境卡规格知识，能够精确验证所有字段格式、逻辑关系和代码映射。',
    FORM_AUDITOR: 'MDAC表单审核专家，专门检查表单数据的完整性、准确性和合规性，确保符合马来西亚官方要求。',
    DATA_EXTRACTOR: '智能数据提取专家，专门从各种文档格式中提取结构化数据，支持中英文混合内容的精确识别。',
    PERSONAL_INFO_EXTRACTOR: '个人信息提取专家，专门从护照、身份证、签证等官方文档中提取个人信息，确保数据准确性和格式标准化。',
    TRAVEL_INFO_EXTRACTOR: '旅行信息提取专家，专门从机票、酒店预订、行程单等文档中提取旅行信息，智能匹配航班、地址和日期。',
    ADDRESS_STANDARDIZER: '马来西亚地址标准化专家，专门处理地址的翻译、标准化和验证，确保地址格式符合MDAC要求。',
    GEOGRAPHIC_VALIDATOR: '马来西亚地理信息验证专家，专门验证州-城市-邮编的一致性，提供准确的地理代码映射。',
    GOOGLE_MAPS_ANALYST: 'Google Maps API集成专家，专门处理地址标准化、地理编码和位置验证，确保地址数据的准确性。',
    DATE_FORMATTER: '日期格式化专家，专门处理各种日期格式的识别和转换，确保输出严格符合DD/MM/YYYY格式。',
    CODE_MAPPER: 'MDAC代码映射专家，专门处理国籍、机场、航空公司等各种代码的智能匹配和验证。',
    API_ANALYST: 'API分析专家，专门分析MDAC网站的API结构和实现方案，提供技术集成建议。',
    CONTENT_OPTIMIZER: 'MDAC内容优化专家，专门优化表单填写内容，提高数据质量和提交成功率。',
    CONNECTION_TESTER: '这是一个AI连接测试助手'
};

// AI功能配置 - 优化版
var AI_FEATURES = {
    // 实时验证增强
    REALTIME_VALIDATION: {
        enabled: true,
        debounceTime: 300, // 减少延迟，提高响应速度
        minFields: 2, // 降低触发门槛
        validateOnBlur: true, // 失焦时验证
        showInlineErrors: true, // 显示内联错误
        autoCorrect: true // 自动修正明显错误
    },

    // 智能内容解析
    CONTENT_PARSING: {
        enabled: true,
        autoTranslate: true,
        supportedLanguages: ['zh-CN', 'en-US', 'ms-MY', 'zh-TW'],
        enhancedRecognition: true, // 增强识别能力
        contextAware: true, // 上下文感知
        multiFormat: true, // 支持多种格式
        confidenceThreshold: 0.7 // 置信度阈值
    },

    // 地址标准化增强
    ADDRESS_STANDARDIZATION: {
        enabled: true,
        autoDetect: true,
        cleanResult: true,
        geocodeValidation: true, // 地理编码验证
        postcodeValidation: true, // 邮编验证
        stateConsistency: true, // 州一致性检查
        googleMapsIntegration: true // Google Maps集成
    },

    // 表单智能优化
    FORM_OPTIMIZATION: {
        enabled: true,
        autoSuggest: true,
        detailedAnalysis: true,
        smartFieldMapping: true, // 智能字段映射
        dataConsistency: true, // 数据一致性检查
        logicValidation: true, // 逻辑验证
        completenessCheck: true // 完整性检查
    },

    // 日期格式化
    DATE_FORMATTING: {
        enabled: true,
        autoDetect: true,
        strictValidation: true, // 严格验证
        logicCheck: true, // 逻辑检查
        rangeValidation: true // 范围验证
    },

    // 代码智能匹配
    CODE_MAPPING: {
        enabled: true,
        fuzzyMatching: true, // 模糊匹配
        multiLanguage: true, // 多语言支持
        autoCorrection: true, // 自动纠错
        confidenceScoring: true // 置信度评分
    },

    // Google Maps API集成
    GOOGLE_MAPS_INTEGRATION: {
        enabled: true,
        addressValidation: true,
        geocoding: true,
        postcodeCompletion: true,
        stateMapping: true,
        cityMapping: true,
        experimental: false, // 已稳定
        requiresPermission: false, // 使用现有API密钥
        cacheResults: true, // 缓存结果
        rateLimitDelay: 100 // API调用间隔
    },

    // API分析
    API_ANALYSIS: {
        enabled: true,
        experimental: true,
        requiresPermission: true,
        networkCapture: true, // 网络请求捕获
        formAnalysis: true // 表单分析
    }
};

// 缓存配置
var CACHE_CONFIG = {
    enabled: true,
    maxSize: 100,
    ttl: 3600000, // 1小时
    keyPrefix: 'mdac_ai_cache_'
};

// 错误处理配置
var ERROR_HANDLING = {
    retryAttempts: 3,
    retryDelay: 1000,
    fallbackToBasic: true,
    logErrors: true
};

// 导出配置 - 使用全局变量方式（兼容Chrome扩展）
// 将配置对象挂载到全局window对象，确保在所有环境中都可访问
if (typeof window !== 'undefined') {
    // 浏览器环境
    window.MDAC_AI_CONFIG = {
        GEMINI_CONFIG,
        AI_PROMPTS,
        AI_CONTEXTS,
        AI_FEATURES,
        CACHE_CONFIG,
        ERROR_HANDLING
    };
} else if (typeof global !== 'undefined') {
    // Node.js环境
    global.MDAC_AI_CONFIG = {
        GEMINI_CONFIG,
        AI_PROMPTS,
        AI_CONTEXTS,
        AI_FEATURES,
        CACHE_CONFIG,
        ERROR_HANDLING
    };
}

// 同时保持向后兼容，直接挂载到全局作用域
this.GEMINI_CONFIG = GEMINI_CONFIG;
this.AI_PROMPTS = AI_PROMPTS;
this.AI_CONTEXTS = AI_CONTEXTS;
this.AI_FEATURES = AI_FEATURES;
this.CACHE_CONFIG = CACHE_CONFIG;
this.ERROR_HANDLING = ERROR_HANDLING;
