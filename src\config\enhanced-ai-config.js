/**
 * MDAC AI智能填充工具 - 增强AI配置文件
 * 深度优化的Gemini AI配置，提升识别准确率和填充成功率
 */

// 增强的国家代码映射表
var ENHANCED_COUNTRY_MAPPING = {
    // 中文名称映射
    "中国": "CHN", "中华人民共和国": "CHN", "大陆": "CHN",
    "美国": "USA", "美利坚合众国": "USA", "美利坚": "USA",
    "新加坡": "SGP", "狮城": "SGP",
    "马来西亚": "MYS", "大马": "MYS",
    "泰国": "THA", "暹罗": "THA",
    "印度尼西亚": "IDN", "印尼": "IDN",
    "日本": "JPN", "倭国": "JPN",
    "韩国": "KOR", "南韩": "KOR", "大韩民国": "KOR",
    "朝鲜": "PRK", "北韩": "PRK", "朝鲜民主主义人民共和国": "PRK",
    "越南": "VNM", "越南社会主义共和国": "VNM",
    "菲律宾": "PHL", "菲律宾共和国": "PHL",
    "缅甸": "MMR", "缅甸联邦共和国": "MMR",
    "柬埔寨": "KHM", "柬埔寨王国": "KHM",
    "老挝": "LAO", "老挝人民民主共和国": "LAO",
    "文莱": "BRN", "文莱达鲁萨兰国": "BRN",
    "印度": "IND", "印度共和国": "IND",
    "巴基斯坦": "PAK", "巴基斯坦伊斯兰共和国": "PAK",
    "孟加拉国": "BGD", "孟加拉人民共和国": "BGD",
    "斯里兰卡": "LKA", "斯里兰卡民主社会主义共和国": "LKA",
    "尼泊尔": "NPL", "尼泊尔联邦民主共和国": "NPL",
    "不丹": "BTN", "不丹王国": "BTN",
    "马尔代夫": "MDV", "马尔代夫共和国": "MDV",
    "阿富汗": "AFG", "阿富汗伊斯兰共和国": "AFG",
    "伊朗": "IRN", "伊朗伊斯兰共和国": "IRN",
    "伊拉克": "IRQ", "伊拉克共和国": "IRQ",
    "土耳其": "TUR", "土耳其共和国": "TUR",
    "沙特阿拉伯": "SAU", "沙特": "SAU",
    "阿联酋": "ARE", "阿拉伯联合酋长国": "ARE",
    "卡塔尔": "QAT", "卡塔尔国": "QAT",
    "科威特": "KWT", "科威特国": "KWT",
    "巴林": "BHR", "巴林王国": "BHR",
    "阿曼": "OMN", "阿曼苏丹国": "OMN",
    "也门": "YEM", "也门共和国": "YEM",
    "约旦": "JOR", "约旦哈希姆王国": "JOR",
    "黎巴嫩": "LBN", "黎巴嫩共和国": "LBN",
    "叙利亚": "SYR", "阿拉伯叙利亚共和国": "SYR",
    "以色列": "ISR", "以色列国": "ISR",
    "巴勒斯坦": "PSE", "巴勒斯坦国": "PSE",
    "塞浦路斯": "CYP", "塞浦路斯共和国": "CYP",

    // 英文名称映射
    "China": "CHN", "People's Republic of China": "CHN", "PRC": "CHN",
    "United States": "USA", "USA": "USA", "US": "USA", "America": "USA",
    "Singapore": "SGP", "Republic of Singapore": "SGP",
    "Malaysia": "MYS", "Federation of Malaysia": "MYS",
    "Thailand": "THA", "Kingdom of Thailand": "THA",
    "Indonesia": "IDN", "Republic of Indonesia": "IDN",
    "Japan": "JPN", "Nippon": "JPN", "Nihon": "JPN",
    "South Korea": "KOR", "Republic of Korea": "KOR", "Korea": "KOR",
    "North Korea": "PRK", "Democratic People's Republic of Korea": "PRK",
    "Vietnam": "VNM", "Socialist Republic of Vietnam": "VNM",
    "Philippines": "PHL", "Republic of the Philippines": "PHL",
    "Myanmar": "MMR", "Republic of the Union of Myanmar": "MMR", "Burma": "MMR",
    "Cambodia": "KHM", "Kingdom of Cambodia": "KHM",
    "Laos": "LAO", "Lao People's Democratic Republic": "LAO",
    "Brunei": "BRN", "Brunei Darussalam": "BRN",
    "India": "IND", "Republic of India": "IND",
    "Pakistan": "PAK", "Islamic Republic of Pakistan": "PAK",
    "Bangladesh": "BGD", "People's Republic of Bangladesh": "BGD",
    "Sri Lanka": "LKA", "Democratic Socialist Republic of Sri Lanka": "LKA",
    "Nepal": "NPL", "Federal Democratic Republic of Nepal": "NPL",
    "Bhutan": "BTN", "Kingdom of Bhutan": "BTN",
    "Maldives": "MDV", "Republic of Maldives": "MDV",
    "Afghanistan": "AFG", "Islamic Republic of Afghanistan": "AFG",
    "Iran": "IRN", "Islamic Republic of Iran": "IRN",
    "Iraq": "IRQ", "Republic of Iraq": "IRQ",
    "Turkey": "TUR", "Republic of Turkey": "TUR",
    "Saudi Arabia": "SAU", "Kingdom of Saudi Arabia": "SAU",
    "United Arab Emirates": "ARE", "UAE": "ARE",
    "Qatar": "QAT", "State of Qatar": "QAT",
    "Kuwait": "KWT", "State of Kuwait": "KWT",
    "Bahrain": "BHR", "Kingdom of Bahrain": "BHR",
    "Oman": "OMN", "Sultanate of Oman": "OMN",
    "Yemen": "YEM", "Republic of Yemen": "YEM",
    "Jordan": "JOR", "Hashemite Kingdom of Jordan": "JOR",
    "Lebanon": "LBN", "Lebanese Republic": "LBN",
    "Syria": "SYR", "Syrian Arab Republic": "SYR",
    "Israel": "ISR", "State of Israel": "ISR",
    "Palestine": "PSE", "State of Palestine": "PSE",
    "Cyprus": "CYP", "Republic of Cyprus": "CYP",

    // 拼音映射
    "zhongguo": "CHN", "meiguo": "USA", "xinjiapo": "SGP",
    "malaixiya": "MYS", "taiguo": "THA", "yindunixiya": "IDN",
    "riben": "JPN", "hanguo": "KOR", "chaoxian": "PRK",
    "yuenan": "VNM", "feilvbin": "PHL", "miandian": "MMR",
    "jianpuzhai": "KHM", "laowo": "LAO", "wenlai": "BRN",
    "yindu": "IND", "bajisitan": "PAK", "mengjiala": "BGD"
};

// 马来西亚州市代码映射表
var MALAYSIA_LOCATION_MAPPING = {
    states: {
        "01": { name: "Johor", chinese: "柔佛", code: "01" },
        "02": { name: "Kedah", chinese: "吉打", code: "02" },
        "03": { name: "Kelantan", chinese: "吉兰丹", code: "03" },
        "04": { name: "Melaka", chinese: "马六甲", code: "04" },
        "05": { name: "Negeri Sembilan", chinese: "森美兰", code: "05" },
        "06": { name: "Pahang", chinese: "彭亨", code: "06" },
        "07": { name: "Penang", chinese: "槟城", code: "07" },
        "08": { name: "Perak", chinese: "霹雳", code: "08" },
        "09": { name: "Perlis", chinese: "玻璃市", code: "09" },
        "10": { name: "Sabah", chinese: "沙巴", code: "10" },
        "11": { name: "Sarawak", chinese: "砂拉越", code: "11" },
        "12": { name: "Selangor", chinese: "雪兰莪", code: "12" },
        "13": { name: "Terengganu", chinese: "登嘉楼", code: "13" },
        "14": { name: "Kuala Lumpur", chinese: "吉隆坡", code: "14" },
        "15": { name: "Labuan", chinese: "纳闽", code: "15" },
        "16": { name: "Putrajaya", chinese: "布城", code: "16" }
    },
    cities: {
        // 吉隆坡
        "1400": { name: "Kuala Lumpur", state: "14", chinese: "吉隆坡" },
        "1401": { name: "Cheras", state: "14", chinese: "蕉赖" },
        "1402": { name: "Ampang", state: "14", chinese: "安邦" },
        "1403": { name: "Petaling Jaya", state: "14", chinese: "八打灵再也" },

        // 槟城
        "1000": { name: "George Town", state: "07", chinese: "乔治市" },
        "1001": { name: "Butterworth", state: "07", chinese: "北海" },
        "1002": { name: "Bukit Mertajam", state: "07", chinese: "大山脚" },

        // 马六甲
        "0700": { name: "Melaka", state: "04", chinese: "马六甲" },
        "0701": { name: "Alor Gajah", state: "04", chinese: "亚罗牙也" },

        // 新山
        "0100": { name: "Johor Bahru", state: "01", chinese: "新山" },
        "0101": { name: "Skudai", state: "01", chinese: "士古来" },
        "0102": { name: "Iskandar Puteri", state: "01", chinese: "依斯干达公主城" },

        // 雪兰莪
        "1200": { name: "Shah Alam", state: "12", chinese: "莎阿南" },
        "1201": { name: "Subang Jaya", state: "12", chinese: "梳邦再也" },
        "1202": { name: "Klang", state: "12", chinese: "巴生" }
    }
};

// 航空公司代码映射表
var AIRLINE_MAPPING = {
    // 马来西亚航空公司
    "MH": { name: "Malaysia Airlines", chinese: "马来西亚航空", country: "MYS" },
    "AK": { name: "AirAsia", chinese: "亚洲航空", country: "MYS" },
    "FY": { name: "Firefly", chinese: "萤火虫航空", country: "MYS" },
    "MV": { name: "MASwings", chinese: "马航之翼", country: "MYS" },

    // 中国航空公司
    "CA": { name: "Air China", chinese: "中国国际航空", country: "CHN" },
    "CZ": { name: "China Southern Airlines", chinese: "中国南方航空", country: "CHN" },
    "MU": { name: "China Eastern Airlines", chinese: "中国东方航空", country: "CHN" },
    "HU": { name: "Hainan Airlines", chinese: "海南航空", country: "CHN" },
    "3U": { name: "Sichuan Airlines", chinese: "四川航空", country: "CHN" },
    "9C": { name: "Spring Airlines", chinese: "春秋航空", country: "CHN" },

    // 新加坡航空公司
    "SQ": { name: "Singapore Airlines", chinese: "新加坡航空", country: "SGP" },
    "TR": { name: "Scoot", chinese: "酷航", country: "SGP" },
    "MI": { name: "SilkAir", chinese: "胜安航空", country: "SGP" },

    // 泰国航空公司
    "TG": { name: "Thai Airways", chinese: "泰国国际航空", country: "THA" },
    "WE": { name: "Thai Smile", chinese: "泰国微笑航空", country: "THA" },
    "FD": { name: "Thai AirAsia", chinese: "泰国亚洲航空", country: "THA" },

    // 印尼航空公司
    "GA": { name: "Garuda Indonesia", chinese: "印尼鹰航", country: "IDN" },
    "JT": { name: "Lion Air", chinese: "狮子航空", country: "IDN" },
    "QZ": { name: "Indonesia AirAsia", chinese: "印尼亚洲航空", country: "IDN" },

    // 其他亚洲航空公司
    "CX": { name: "Cathay Pacific", chinese: "国泰航空", country: "HKG" },
    "KA": { name: "Cathay Dragon", chinese: "国泰港龙航空", country: "HKG" },
    "BR": { name: "EVA Air", chinese: "长荣航空", country: "TWN" },
    "CI": { name: "China Airlines", chinese: "中华航空", country: "TWN" },
    "NH": { name: "All Nippon Airways", chinese: "全日空", country: "JPN" },
    "JL": { name: "Japan Airlines", chinese: "日本航空", country: "JPN" },
    "KE": { name: "Korean Air", chinese: "大韩航空", country: "KOR" },
    "OZ": { name: "Asiana Airlines", chinese: "韩亚航空", country: "KOR" },

    // 欧美航空公司
    "EK": { name: "Emirates", chinese: "阿联酋航空", country: "ARE" },
    "QR": { name: "Qatar Airways", chinese: "卡塔尔航空", country: "QAT" },
    "EY": { name: "Etihad Airways", chinese: "阿提哈德航空", country: "ARE" },
    "LH": { name: "Lufthansa", chinese: "汉莎航空", country: "DEU" },
    "BA": { name: "British Airways", chinese: "英国航空", country: "GBR" },
    "AF": { name: "Air France", chinese: "法国航空", country: "FRA" },
    "KL": { name: "KLM", chinese: "荷兰皇家航空", country: "NLD" },
    "UA": { name: "United Airlines", chinese: "美国联合航空", country: "USA" },
    "AA": { name: "American Airlines", chinese: "美国航空", country: "USA" },
    "DL": { name: "Delta Air Lines", chinese: "达美航空", country: "USA" }
};

// 机场代码映射表
var AIRPORT_MAPPING = {
    // 马来西亚机场
    "KUL": { name: "Kuala Lumpur International Airport", chinese: "吉隆坡国际机场", city: "Kuala Lumpur", state: "14" },
    "SZB": { name: "Sultan Abdul Aziz Shah Airport", chinese: "梳邦机场", city: "Kuala Lumpur", state: "14" },
    "PEN": { name: "Penang International Airport", chinese: "槟城国际机场", city: "George Town", state: "07" },
    "JHB": { name: "Senai International Airport", chinese: "士乃国际机场", city: "Johor Bahru", state: "01" },
    "MKZ": { name: "Malacca Airport", chinese: "马六甲机场", city: "Melaka", state: "04" },
    "KCH": { name: "Kuching International Airport", chinese: "古晋国际机场", city: "Kuching", state: "11" },
    "BKI": { name: "Kota Kinabalu International Airport", chinese: "亚庇国际机场", city: "Kota Kinabalu", state: "10" },

    // 中国主要机场
    "PEK": { name: "Beijing Capital International Airport", chinese: "北京首都国际机场", city: "Beijing" },
    "PVG": { name: "Shanghai Pudong International Airport", chinese: "上海浦东国际机场", city: "Shanghai" },
    "SHA": { name: "Shanghai Hongqiao International Airport", chinese: "上海虹桥国际机场", city: "Shanghai" },
    "CAN": { name: "Guangzhou Baiyun International Airport", chinese: "广州白云国际机场", city: "Guangzhou" },
    "SZX": { name: "Shenzhen Bao'an International Airport", chinese: "深圳宝安国际机场", city: "Shenzhen" },
    "CTU": { name: "Chengdu Shuangliu International Airport", chinese: "成都双流国际机场", city: "Chengdu" },
    "XIY": { name: "Xi'an Xianyang International Airport", chinese: "西安咸阳国际机场", city: "Xi'an" },
    "KMG": { name: "Kunming Changshui International Airport", chinese: "昆明长水国际机场", city: "Kunming" },

    // 新加坡机场
    "SIN": { name: "Singapore Changi Airport", chinese: "新加坡樟宜机场", city: "Singapore" },

    // 泰国机场
    "BKK": { name: "Suvarnabhumi Airport", chinese: "素万那普机场", city: "Bangkok" },
    "DMK": { name: "Don Mueang International Airport", chinese: "廊曼国际机场", city: "Bangkok" },

    // 印尼机场
    "CGK": { name: "Soekarno-Hatta International Airport", chinese: "苏加诺-哈达国际机场", city: "Jakarta" },
    "DPS": { name: "Ngurah Rai International Airport", chinese: "伍拉·赖国际机场", city: "Denpasar" },

    // 其他亚洲机场
    "HKG": { name: "Hong Kong International Airport", chinese: "香港国际机场", city: "Hong Kong" },
    "TPE": { name: "Taiwan Taoyuan International Airport", chinese: "台湾桃园国际机场", city: "Taipei" },
    "NRT": { name: "Narita International Airport", chinese: "成田国际机场", city: "Tokyo" },
    "ICN": { name: "Incheon International Airport", chinese: "仁川国际机场", city: "Seoul" }
};

// 增强的日期格式识别模式
var DATE_PATTERNS = {
    // 标准格式
    "DD/MM/YYYY": /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/,
    "DD-MM-YYYY": /^(\d{1,2})-(\d{1,2})-(\d{4})$/,
    "DD.MM.YYYY": /^(\d{1,2})\.(\d{1,2})\.(\d{4})$/,

    // 中文格式
    "YYYY年MM月DD日": /^(\d{4})年(\d{1,2})月(\d{1,2})日$/,
    "YYYY-MM-DD": /^(\d{4})-(\d{1,2})-(\d{1,2})$/,
    "YYYY/MM/DD": /^(\d{4})\/(\d{1,2})\/(\d{1,2})$/,

    // 英文格式
    "MM/DD/YYYY": /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/,
    "Month DD, YYYY": /^([A-Za-z]+)\s+(\d{1,2}),\s+(\d{4})$/,
    "DD Month YYYY": /^(\d{1,2})\s+([A-Za-z]+)\s+(\d{4})$/,

    // 简化格式
    "DDMMYYYY": /^(\d{2})(\d{2})(\d{4})$/,
    "YYYYMMDD": /^(\d{4})(\d{2})(\d{2})$/,

    // 相对格式
    "今天": "TODAY",
    "明天": "TOMORROW",
    "昨天": "YESTERDAY"
};

// 月份名称映射
var MONTH_MAPPING = {
    // 英文月份
    "January": "01", "Jan": "01",
    "February": "02", "Feb": "02",
    "March": "03", "Mar": "03",
    "April": "04", "Apr": "04",
    "May": "05",
    "June": "06", "Jun": "06",
    "July": "07", "Jul": "07",
    "August": "08", "Aug": "08",
    "September": "09", "Sep": "09", "Sept": "09",
    "October": "10", "Oct": "10",
    "November": "11", "Nov": "11",
    "December": "12", "Dec": "12",

    // 中文月份
    "一月": "01", "二月": "02", "三月": "03", "四月": "04",
    "五月": "05", "六月": "06", "七月": "07", "八月": "08",
    "九月": "09", "十月": "10", "十一月": "11", "十二月": "12"
};

// 增强的AI提示词模板
var ENHANCED_AI_PROMPTS = {
    // 超级智能个人信息解析
    SUPER_PERSONAL_INFO_PARSING: `你是MDAC表单个人信息提取专家。请从以下内容中精确提取个人信息：

输入内容：
{content}

提取规则：
1. **姓名识别**：
   - 优先提取护照上的英文姓名
   - 中文姓名转换为标准拼音格式（姓在前，名在后，首字母大写）
   - 格式：Zhang Wei, Li Xiaoming, Wang Jiahao
   - 最大长度：50字符，只允许字母、空格、逗号、点号

2. **护照号码识别**：
   - 格式：1-2个字母 + 6-9位数字
   - 示例：A1234567, AB123456789, P12345678
   - 自动去除空格和特殊字符

3. **日期智能识别**：
   - 支持格式：${Object.keys(DATE_PATTERNS).join(', ')}
   - 输出格式：严格DD/MM/YYYY
   - 逻辑验证：出生日期 < 护照到期日期 < 当前日期+10年

4. **国籍智能映射**：
   - 支持中英文国家名称识别
   - 自动映射到3位ISO代码
   - 常见映射：${Object.keys(ENHANCED_COUNTRY_MAPPING).slice(0, 10).map(k => `${k}→${ENHANCED_COUNTRY_MAPPING[k]}`).join(', ')}

5. **性别智能判断**：
   - 从姓名、称谓、护照信息推断
   - 输出：1=男性，2=女性
   - 关键词：先生/Mr→1，女士/Ms/Mrs→2

6. **联系方式提取**：
   - 邮箱：标准格式验证
   - 手机：提取纯数字，自动识别国家代码
   - 国家代码映射：中国+86，马来西亚+60，美国+1，新加坡+65

输出要求：
- 严格JSON格式，无markdown标记
- 无法确定的字段设为null
- 所有文本转换为英文
- 日期格式统一为DD/MM/YYYY

输出字段：
{
  "name": "英文全名",
  "passportNo": "护照号码",
  "dateOfBirth": "DD/MM/YYYY",
  "nationality": "3位ISO代码",
  "sex": "1或2",
  "passportExpiry": "DD/MM/YYYY",
  "email": "邮箱地址",
  "confirmEmail": "确认邮箱",
  "countryCode": "国家代码",
  "mobileNo": "纯数字手机号"
}`,

    // 超级智能旅行信息解析
    SUPER_TRAVEL_INFO_PARSING: `你是MDAC表单旅行信息提取专家。请从以下内容中精确提取旅行信息：

输入内容：
{content}

提取规则：
1. **日期智能识别**：
   - 到达日期：入境马来西亚的日期
   - 离开日期：出境马来西亚的日期
   - 支持格式：${Object.keys(DATE_PATTERNS).join(', ')}
   - 输出格式：严格DD/MM/YYYY
   - 逻辑验证：到达日期 < 离开日期，且在合理时间范围内

2. **航班号智能识别**：
   - 格式：航空公司代码(2位字母) + 数字
   - 航空公司映射：${Object.keys(AIRLINE_MAPPING).slice(0, 10).map(k => `${k}=${AIRLINE_MAPPING[k].chinese}`).join(', ')}
   - 示例：MH123, CZ351, SQ456, AK789
   - 自动识别交通工具类型

3. **旅行方式智能判断**：
   - AIR：航空（有航班号或机票信息）
   - LAND：陆路（巴士、火车、汽车）
   - SEA：海路（船舶、渡轮）
   - 根据交通工具信息自动判断

4. **港口/机场代码识别**：
   - 最后出发地的3位代码
   - 机场映射：${Object.keys(AIRPORT_MAPPING).slice(0, 8).map(k => `${k}=${AIRPORT_MAPPING[k].chinese}`).join(', ')}
   - 自动识别城市对应的主要机场

5. **住宿信息智能提取**：
   - 住宿类型：01=酒店，02=朋友家，03=民宿，04=亲戚家，99=其他
   - 地址提取：完整的马来西亚地址
   - 州市代码映射：${Object.keys(MALAYSIA_LOCATION_MAPPING.states).slice(0, 5).map(k => `${k}=${MALAYSIA_LOCATION_MAPPING.states[k].chinese}`).join(', ')}

6. **地址标准化**：
   - 中文地址翻译为英文
   - 提取州、城市、邮政编码
   - 地址格式：街道号码 + 街道名称 + 区域 + 城市
   - 邮政编码：5位数字

输出要求：
- 严格JSON格式，无markdown标记
- 地址必须为英文且符合马来西亚格式
- 日期格式统一为DD/MM/YYYY
- 代码使用标准映射值

输出字段：
{
  "arrivalDate": "DD/MM/YYYY",
  "departureDate": "DD/MM/YYYY",
  "flightNo": "航班号",
  "modeOfTravel": "AIR/LAND/SEA",
  "lastPort": "3位机场代码",
  "accommodation": "住宿类型代码",
  "address": "英文地址行1",
  "address2": "英文地址行2",
  "state": "州代码",
  "city": "城市代码",
  "postcode": "5位邮编"
}`,

    // Google Maps地址标准化增强提示词
    GOOGLE_MAPS_ENHANCED_STANDARDIZATION: `你是Google Maps地址标准化专家，专门处理马来西亚地址的标准化和验证。

输入地址：{address}
Google Maps结果：{mapsResult}

分析任务：
1. **地址质量评估**：
   - 完整性：地址组件是否完整
   - 准确性：是否在马来西亚境内
   - 标准性：格式是否符合MDAC要求
   - 可信度：Google Maps置信度评分

2. **地址组件提取**：
   - 街道号码和名称
   - 区域/地区名称
   - 城市名称
   - 州名称
   - 邮政编码

3. **MDAC字段映射**：
   - address: 街道地址（英文）
   - address2: 补充地址（可选）
   - state: 州代码（${Object.keys(MALAYSIA_LOCATION_MAPPING.states).slice(0, 8).join(', ')}）
   - city: 城市代码
   - postcode: 5位邮政编码

4. **数据验证**：
   - 州-城市-邮编一致性检查
   - 地址存在性验证
   - 格式规范性检查

5. **改进建议**：
   - 地址标准化建议
   - 缺失信息补全
   - 格式优化建议

输出格式：
{
  "standardizedAddress": "标准化完整地址",
  "mdacMapping": {
    "address": "街道地址",
    "address2": "补充地址或null",
    "state": "州代码",
    "city": "城市代码",
    "postcode": "邮政编码"
  },
  "quality": {
    "completeness": 0.95,
    "accuracy": 0.98,
    "confidence": 0.92,
    "isValid": true
  },
  "validation": {
    "inMalaysia": true,
    "stateConsistent": true,
    "postcodeValid": true,
    "formatCorrect": true
  },
  "suggestions": [
    "建议1：地址格式优化",
    "建议2：缺失信息补全"
  ]
}`
};