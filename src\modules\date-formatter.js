/**
 * MDAC AI智能分析工具 - 智能日期格式化器
 * 支持多种日期格式的智能识别和转换，确保输出符合MDAC要求的DD/MM/YYYY格式
 */

class DateFormatter {
    constructor() {
        // 日期格式模式
        this.patterns = {
            // 标准分隔符格式
            'DD/MM/YYYY': /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/,
            'DD-MM-YYYY': /^(\d{1,2})-(\d{1,2})-(\d{4})$/,
            'DD.MM.YYYY': /^(\d{1,2})\.(\d{1,2})\.(\d{4})$/,
            'DD MM YYYY': /^(\d{1,2})\s+(\d{1,2})\s+(\d{4})$/,
            
            // 年月日格式
            'YYYY/MM/DD': /^(\d{4})\/(\d{1,2})\/(\d{1,2})$/,
            'YYYY-MM-DD': /^(\d{4})-(\d{1,2})-(\d{1,2})$/,
            'YYYY.MM.DD': /^(\d{4})\.(\d{1,2})\.(\d{1,2})$/,
            'YYYY MM DD': /^(\d{4})\s+(\d{1,2})\s+(\d{1,2})$/,
            
            // 美式格式
            'MM/DD/YYYY': /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/,
            'MM-DD-YYYY': /^(\d{1,2})-(\d{1,2})-(\d{4})$/,
            'MM.DD.YYYY': /^(\d{1,2})\.(\d{1,2})\.(\d{4})$/,
            
            // 中文格式
            'YYYY年MM月DD日': /^(\d{4})年(\d{1,2})月(\d{1,2})日$/,
            'YYYY年M月D日': /^(\d{4})年(\d{1,2})月(\d{1,2})日$/,
            
            // 紧凑格式
            'DDMMYYYY': /^(\d{2})(\d{2})(\d{4})$/,
            'YYYYMMDD': /^(\d{4})(\d{2})(\d{2})$/,
            'MMDDYYYY': /^(\d{2})(\d{2})(\d{4})$/,
            
            // 英文月份格式
            'DD Month YYYY': /^(\d{1,2})\s+([A-Za-z]+)\s+(\d{4})$/,
            'Month DD, YYYY': /^([A-Za-z]+)\s+(\d{1,2}),?\s+(\d{4})$/,
            'DD-Month-YYYY': /^(\d{1,2})-([A-Za-z]+)-(\d{4})$/,
            'Month DD YYYY': /^([A-Za-z]+)\s+(\d{1,2})\s+(\d{4})$/
        };
        
        // 月份名称映射
        this.monthMapping = {
            // 英文月份全称
            'january': '01', 'february': '02', 'march': '03', 'april': '04',
            'may': '05', 'june': '06', 'july': '07', 'august': '08',
            'september': '09', 'october': '10', 'november': '11', 'december': '12',
            
            // 英文月份缩写
            'jan': '01', 'feb': '02', 'mar': '03', 'apr': '04',
            'may': '05', 'jun': '06', 'jul': '07', 'aug': '08',
            'sep': '09', 'sept': '09', 'oct': '10', 'nov': '11', 'dec': '12',
            
            // 中文月份
            '一月': '01', '二月': '02', '三月': '03', '四月': '04',
            '五月': '05', '六月': '06', '七月': '07', '八月': '08',
            '九月': '09', '十月': '10', '十一月': '11', '十二月': '12',
            
            // 数字月份
            '1': '01', '2': '02', '3': '03', '4': '04', '5': '05', '6': '06',
            '7': '07', '8': '08', '9': '09', '10': '10', '11': '11', '12': '12'
        };
        
        // 相对日期关键词
        this.relativeKeywords = {
            '今天': 0, '今日': 0, 'today': 0,
            '明天': 1, '明日': 1, 'tomorrow': 1,
            '昨天': -1, '昨日': -1, 'yesterday': -1,
            '后天': 2, 'day after tomorrow': 2,
            '前天': -2, 'day before yesterday': -2
        };
        
        // 统计信息
        this.stats = {
            totalAttempts: 0,
            successfulParsing: 0,
            failedParsing: 0,
            formatDistribution: {}
        };
    }
    
    /**
     * 智能解析日期字符串
     * @param {string} dateStr - 输入的日期字符串
     * @param {string} context - 上下文信息（birth/expiry/arrival/departure）
     * @returns {Object} 解析结果
     */
    parseDate(dateStr, context = 'general') {
        this.stats.totalAttempts++;
        
        if (!dateStr || typeof dateStr !== 'string') {
            this.stats.failedParsing++;
            return { success: false, error: '无效的日期输入' };
        }
        
        // 清理输入字符串
        const cleanStr = this.cleanDateString(dateStr);
        
        // 检查相对日期
        const relativeResult = this.parseRelativeDate(cleanStr);
        if (relativeResult.success) {
            this.stats.successfulParsing++;
            return relativeResult;
        }
        
        // 尝试各种格式模式
        for (const [formatName, pattern] of Object.entries(this.patterns)) {
            const result = this.tryParseWithPattern(cleanStr, pattern, formatName);
            if (result.success) {
                // 验证日期逻辑
                const validation = this.validateDate(result.parsed, context);
                if (validation.isValid) {
                    this.stats.successfulParsing++;
                    this.updateFormatStats(formatName);
                    return {
                        success: true,
                        formatted: result.formatted,
                        original: dateStr,
                        detected_format: formatName,
                        parsed: result.parsed,
                        validation: validation
                    };
                }
            }
        }
        
        // 尝试智能猜测
        const guessResult = this.intelligentGuess(cleanStr, context);
        if (guessResult.success) {
            this.stats.successfulParsing++;
            return guessResult;
        }
        
        this.stats.failedParsing++;
        return {
            success: false,
            error: '无法识别的日期格式',
            original: dateStr,
            suggestions: this.generateSuggestions(cleanStr)
        };
    }
    
    /**
     * 清理日期字符串
     */
    cleanDateString(dateStr) {
        return dateStr
            .trim()
            .replace(/[，。、]/g, ',') // 中文标点转英文
            .replace(/\s+/g, ' ') // 多个空格合并
            .replace(/[^\w\s\/\-\.\,年月日]/g, ''); // 移除特殊字符
    }
    
    /**
     * 解析相对日期
     */
    parseRelativeDate(dateStr) {
        const lowerStr = dateStr.toLowerCase();
        
        for (const [keyword, offset] of Object.entries(this.relativeKeywords)) {
            if (lowerStr.includes(keyword)) {
                const targetDate = new Date();
                targetDate.setDate(targetDate.getDate() + offset);
                
                const day = String(targetDate.getDate()).padStart(2, '0');
                const month = String(targetDate.getMonth() + 1).padStart(2, '0');
                const year = targetDate.getFullYear();
                
                return {
                    success: true,
                    formatted: `${day}/${month}/${year}`,
                    original: dateStr,
                    detected_format: 'relative',
                    parsed: { day, month, year },
                    validation: { isValid: true, type: 'relative' }
                };
            }
        }
        
        return { success: false };
    }
    
    /**
     * 使用特定模式尝试解析
     */
    tryParseWithPattern(dateStr, pattern, formatName) {
        const match = dateStr.match(pattern);
        if (!match) {
            return { success: false };
        }
        
        let day, month, year;
        
        // 根据格式名称确定字段顺序
        if (formatName.includes('YYYY') && formatName.indexOf('YYYY') === 0) {
            // 年在前的格式
            year = match[1];
            month = match[2];
            day = match[3];
        } else if (formatName.includes('MM/DD') || formatName.includes('MM-DD')) {
            // 美式格式 MM/DD/YYYY
            month = match[1];
            day = match[2];
            year = match[3];
        } else if (formatName.includes('Month')) {
            // 包含月份名称的格式
            if (formatName.startsWith('DD')) {
                // DD Month YYYY
                day = match[1];
                month = this.monthMapping[match[2].toLowerCase()];
                year = match[3];
            } else {
                // Month DD, YYYY
                month = this.monthMapping[match[1].toLowerCase()];
                day = match[2];
                year = match[3];
            }
        } else {
            // 默认 DD/MM/YYYY 格式
            day = match[1];
            month = match[2];
            year = match[3];
        }
        
        // 标准化数字格式
        day = String(parseInt(day)).padStart(2, '0');
        month = String(parseInt(month)).padStart(2, '0');
        year = String(parseInt(year));
        
        // 验证基本范围
        if (parseInt(day) < 1 || parseInt(day) > 31 ||
            parseInt(month) < 1 || parseInt(month) > 12 ||
            parseInt(year) < 1900 || parseInt(year) > 2100) {
            return { success: false };
        }
        
        return {
            success: true,
            formatted: `${day}/${month}/${year}`,
            parsed: { day, month, year }
        };
    }
    
    /**
     * 验证日期逻辑
     */
    validateDate(parsed, context) {
        const { day, month, year } = parsed;
        const date = new Date(year, month - 1, day);
        const now = new Date();
        
        // 基本有效性检查
        if (date.getDate() != day || date.getMonth() != month - 1 || date.getFullYear() != year) {
            return { isValid: false, error: '日期不存在' };
        }
        
        // 上下文相关验证
        switch (context) {
            case 'birth':
                // 出生日期应该在过去，且不超过150年
                const minBirthYear = now.getFullYear() - 150;
                if (date > now) {
                    return { isValid: false, error: '出生日期不能是未来' };
                }
                if (year < minBirthYear) {
                    return { isValid: false, error: '出生日期过于久远' };
                }
                break;
                
            case 'expiry':
                // 护照到期日应该在未来，且不超过20年
                const maxExpiryYear = now.getFullYear() + 20;
                if (date < now) {
                    return { isValid: false, error: '护照已过期' };
                }
                if (year > maxExpiryYear) {
                    return { isValid: false, error: '护照有效期过长' };
                }
                break;
                
            case 'arrival':
            case 'departure':
                // 旅行日期应该在合理范围内（过去1年到未来2年）
                const minTravelDate = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
                const maxTravelDate = new Date(now.getFullYear() + 2, now.getMonth(), now.getDate());
                if (date < minTravelDate || date > maxTravelDate) {
                    return { isValid: false, error: '旅行日期超出合理范围' };
                }
                break;
        }
        
        return { isValid: true, context: context };
    }
    
    /**
     * 智能猜测日期格式
     */
    intelligentGuess(dateStr, context) {
        // 提取所有数字
        const numbers = dateStr.match(/\d+/g);
        if (!numbers || numbers.length < 3) {
            return { success: false };
        }
        
        // 尝试不同的组合
        const combinations = [
            [numbers[0], numbers[1], numbers[2]], // DD MM YYYY
            [numbers[1], numbers[0], numbers[2]], // MM DD YYYY
            [numbers[2], numbers[1], numbers[0]], // YYYY MM DD
            [numbers[2], numbers[0], numbers[1]]  // YYYY DD MM
        ];
        
        for (const [a, b, c] of combinations) {
            // 判断哪个可能是年份
            if (parseInt(c) > 1900 && parseInt(c) < 2100) {
                // c是年份
                const result = this.tryParseWithPattern(`${a}/${b}/${c}`, this.patterns['DD/MM/YYYY'], 'DD/MM/YYYY');
                if (result.success) {
                    const validation = this.validateDate(result.parsed, context);
                    if (validation.isValid) {
                        return {
                            success: true,
                            formatted: result.formatted,
                            original: dateStr,
                            detected_format: 'intelligent_guess',
                            parsed: result.parsed,
                            validation: validation,
                            confidence: 0.7
                        };
                    }
                }
            }
        }
        
        return { success: false };
    }
    
    /**
     * 生成格式建议
     */
    generateSuggestions(dateStr) {
        return [
            '请使用DD/MM/YYYY格式，如：25/12/2023',
            '支持的格式：DD-MM-YYYY, YYYY/MM/DD, DD Month YYYY',
            '中文格式：2023年12月25日',
            '相对日期：今天、明天、昨天'
        ];
    }
    
    /**
     * 更新格式统计
     */
    updateFormatStats(formatName) {
        if (!this.stats.formatDistribution[formatName]) {
            this.stats.formatDistribution[formatName] = 0;
        }
        this.stats.formatDistribution[formatName]++;
    }
    
    /**
     * 获取统计信息
     */
    getStats() {
        return {
            ...this.stats,
            successRate: this.stats.totalAttempts > 0 ? 
                (this.stats.successfulParsing / this.stats.totalAttempts * 100).toFixed(2) + '%' : '0%'
        };
    }
    
    /**
     * 批量转换日期
     */
    batchConvert(dates, context = 'general') {
        const results = [];
        for (const dateStr of dates) {
            results.push(this.parseDate(dateStr, context));
        }
        return results;
    }
    
    /**
     * 验证日期逻辑关系
     */
    validateDateRelationship(dates) {
        const { birth, expiry, arrival, departure } = dates;
        const errors = [];
        
        if (birth && expiry) {
            const birthDate = this.parseDate(birth, 'birth');
            const expiryDate = this.parseDate(expiry, 'expiry');
            
            if (birthDate.success && expiryDate.success) {
                const birthTime = new Date(birthDate.parsed.year, birthDate.parsed.month - 1, birthDate.parsed.day);
                const expiryTime = new Date(expiryDate.parsed.year, expiryDate.parsed.month - 1, expiryDate.parsed.day);
                
                if (birthTime >= expiryTime) {
                    errors.push('护照到期日期必须晚于出生日期');
                }
            }
        }
        
        if (arrival && departure) {
            const arrivalDate = this.parseDate(arrival, 'arrival');
            const departureDate = this.parseDate(departure, 'departure');
            
            if (arrivalDate.success && departureDate.success) {
                const arrivalTime = new Date(arrivalDate.parsed.year, arrivalDate.parsed.month - 1, arrivalDate.parsed.day);
                const departureTime = new Date(departureDate.parsed.year, departureDate.parsed.month - 1, departureDate.parsed.day);
                
                if (arrivalTime >= departureTime) {
                    errors.push('离开日期必须晚于到达日期');
                }
            }
        }
        
        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }
}

// 导出类
if (typeof window !== 'undefined') {
    window.DateFormatter = DateFormatter;
} else if (typeof module !== 'undefined' && module.exports) {
    module.exports = DateFormatter;
}
