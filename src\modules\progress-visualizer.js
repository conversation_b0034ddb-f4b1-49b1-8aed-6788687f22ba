/**
 * MDAC AI智能分析工具 - 表单填充进度可视化系统
 * 实现表单填充过程的可视化进度显示，增强用户体验
 */

class ProgressVisualizer {
    constructor() {
        // 进度显示配置
        this.config = {
            updateInterval: 100,        // 更新间隔（毫秒）
            animationDuration: 300,     // 动画持续时间（毫秒）
            showFieldDetails: true,     // 显示字段详情
            showTimeEstimate: true,     // 显示时间估算
            enableSoundEffects: false,  // 启用声音效果
            compactMode: false          // 紧凑模式
        };
        
        // 进度状态
        this.progressState = {
            isActive: false,
            startTime: null,
            currentStep: 0,
            totalSteps: 0,
            completedSteps: 0,
            failedSteps: 0,
            skippedSteps: 0,
            currentField: null,
            estimatedTimeRemaining: 0
        };
        
        // 字段状态映射
        this.fieldStates = {
            pending: { icon: '⏳', color: '#6c757d', label: '等待中' },
            processing: { icon: '🔄', color: '#007bff', label: '处理中' },
            success: { icon: '✅', color: '#28a745', label: '成功' },
            failed: { icon: '❌', color: '#dc3545', label: '失败' },
            skipped: { icon: '⏭️', color: '#ffc107', label: '跳过' },
            warning: { icon: '⚠️', color: '#fd7e14', label: '警告' }
        };
        
        // 进度可视化元素
        this.elements = {
            container: null,
            progressBar: null,
            statusText: null,
            fieldList: null,
            timeEstimate: null,
            actionButtons: null
        };
        
        // 字段处理历史
        this.fieldHistory = [];
        
        // 性能统计
        this.performanceStats = {
            averageFieldTime: 200,  // 平均字段处理时间（毫秒）
            successRate: 0.95,      // 成功率
            totalProcessed: 0       // 总处理字段数
        };
        
        // 动画队列
        this.animationQueue = [];
        this.isAnimating = false;
        
        this.init();
    }
    
    /**
     * 初始化进度可视化器
     */
    init() {
        this.loadConfig();
        this.setupStyles();
        console.log('✅ 进度可视化器初始化完成');
    }
    
    /**
     * 开始进度可视化
     * @param {Object} options 配置选项
     */
    startProgress(options = {}) {
        const {
            totalFields = 0,
            fieldList = [],
            title = '表单填充进度',
            showInSidepanel = false,
            position = 'overlay'
        } = options;
        
        // 重置状态
        this.resetProgress();
        
        // 设置进度状态
        this.progressState = {
            isActive: true,
            startTime: Date.now(),
            currentStep: 0,
            totalSteps: totalFields,
            completedSteps: 0,
            failedSteps: 0,
            skippedSteps: 0,
            currentField: null,
            estimatedTimeRemaining: this.calculateEstimatedTime(totalFields)
        };
        
        // 创建进度界面
        if (showInSidepanel) {
            this.createSidepanelProgress(title, fieldList);
        } else {
            this.createOverlayProgress(title, fieldList, position);
        }
        
        console.log(`🚀 开始进度可视化: ${totalFields}个字段`);
        
        return this.elements.container?.id || null;
    }
    
    /**
     * 创建覆盖层进度显示
     */
    createOverlayProgress(title, fieldList, position) {
        // 移除现有的进度显示
        this.removeExistingProgress();
        
        const container = document.createElement('div');
        container.id = 'mdac-progress-visualizer';
        container.className = `progress-visualizer ${position}`;
        
        container.innerHTML = `
            <div class="progress-container">
                <div class="progress-header">
                    <div class="progress-title">
                        <span class="progress-icon">🤖</span>
                        <h3>${title}</h3>
                    </div>
                    <div class="progress-controls">
                        <button class="control-btn minimize" onclick="window.progressVisualizer.toggleMinimize()" title="最小化">
                            <span class="btn-icon">➖</span>
                        </button>
                        <button class="control-btn close" onclick="window.progressVisualizer.closeProgress()" title="关闭">
                            <span class="btn-icon">✕</span>
                        </button>
                    </div>
                </div>
                
                <div class="progress-content">
                    <div class="progress-summary">
                        <div class="progress-stats">
                            <div class="stat-item">
                                <span class="stat-value" id="completedCount">0</span>
                                <span class="stat-label">已完成</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-value" id="failedCount">0</span>
                                <span class="stat-label">失败</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-value" id="remainingCount">${this.progressState.totalSteps}</span>
                                <span class="stat-label">剩余</span>
                            </div>
                        </div>
                        
                        <div class="progress-bar-container">
                            <div class="progress-bar" id="mainProgressBar">
                                <div class="progress-fill" style="width: 0%"></div>
                                <div class="progress-text">0%</div>
                            </div>
                        </div>
                        
                        <div class="progress-status">
                            <span class="status-text" id="statusText">准备开始...</span>
                            <span class="time-estimate" id="timeEstimate">预计剩余: ${this.formatTime(this.progressState.estimatedTimeRemaining)}</span>
                        </div>
                    </div>
                    
                    ${this.config.showFieldDetails ? `
                        <div class="field-progress">
                            <div class="field-header">
                                <h4>字段处理详情</h4>
                                <button class="toggle-btn" onclick="window.progressVisualizer.toggleFieldDetails()">
                                    <span class="toggle-icon">🔽</span>
                                </button>
                            </div>
                            <div class="field-list" id="fieldList">
                                ${this.renderFieldList(fieldList)}
                            </div>
                        </div>
                    ` : ''}
                </div>
                
                <div class="progress-actions">
                    <button class="action-btn secondary" onclick="window.progressVisualizer.pauseProgress()">
                        <span class="btn-icon">⏸️</span>
                        <span class="btn-text">暂停</span>
                    </button>
                    <button class="action-btn danger" onclick="window.progressVisualizer.cancelProgress()">
                        <span class="btn-icon">⏹️</span>
                        <span class="btn-text">取消</span>
                    </button>
                </div>
            </div>
        `;
        
        // 设置位置
        this.setProgressPosition(container, position);
        
        // 插入到页面
        document.body.appendChild(container);
        
        // 缓存元素引用
        this.cacheElements(container);
        
        // 暴露到全局
        window.progressVisualizer = this;
        
        // 添加显示动画
        setTimeout(() => {
            container.classList.add('show');
        }, 10);
    }
    
    /**
     * 创建侧边栏进度显示
     */
    createSidepanelProgress(title, fieldList) {
        const sidepanel = document.getElementById('parseResults') || document.body;
        
        // 创建或更新进度区域
        let progressSection = sidepanel.querySelector('.sidepanel-progress');
        if (!progressSection) {
            progressSection = document.createElement('div');
            progressSection.className = 'sidepanel-progress';
            sidepanel.insertBefore(progressSection, sidepanel.firstChild);
        }
        
        progressSection.innerHTML = `
            <div class="sidepanel-progress-header">
                <h4>${title}</h4>
                <div class="progress-percentage">0%</div>
            </div>
            
            <div class="sidepanel-progress-bar">
                <div class="progress-fill" style="width: 0%"></div>
            </div>
            
            <div class="sidepanel-progress-status">
                <span class="status-text">准备开始...</span>
                <span class="field-counter">0 / ${this.progressState.totalSteps}</span>
            </div>
            
            ${this.config.showFieldDetails ? `
                <div class="sidepanel-field-list">
                    ${this.renderCompactFieldList(fieldList)}
                </div>
            ` : ''}
        `;
        
        this.elements.container = progressSection;
        this.cacheElements(progressSection);
    }
    
    /**
     * 渲染字段列表
     */
    renderFieldList(fieldList) {
        if (!fieldList || fieldList.length === 0) {
            return '<div class="no-fields">暂无字段信息</div>';
        }
        
        return fieldList.map((field, index) => {
            const fieldKey = typeof field === 'string' ? field : field.key;
            const fieldLabel = typeof field === 'object' ? field.label : this.getFieldLabel(fieldKey);
            const fieldValue = typeof field === 'object' ? field.value : '';
            
            return `
                <div class="field-item" data-field="${fieldKey}" data-index="${index}">
                    <div class="field-status">
                        <span class="status-icon">⏳</span>
                    </div>
                    <div class="field-info">
                        <div class="field-name">${fieldLabel}</div>
                        <div class="field-value">${fieldValue || '待填充'}</div>
                    </div>
                    <div class="field-progress">
                        <div class="mini-progress-bar">
                            <div class="mini-progress-fill" style="width: 0%"></div>
                        </div>
                    </div>
                </div>
            `;
        }).join('');
    }
    
    /**
     * 渲染紧凑字段列表
     */
    renderCompactFieldList(fieldList) {
        if (!fieldList || fieldList.length === 0) {
            return '<div class="no-fields">暂无字段信息</div>';
        }
        
        return fieldList.map((field, index) => {
            const fieldKey = typeof field === 'string' ? field : field.key;
            const fieldLabel = typeof field === 'object' ? field.label : this.getFieldLabel(fieldKey);
            
            return `
                <div class="compact-field-item" data-field="${fieldKey}" data-index="${index}">
                    <span class="field-status-icon">⏳</span>
                    <span class="field-name">${fieldLabel}</span>
                </div>
            `;
        }).join('');
    }
    
    /**
     * 更新字段状态
     * @param {string} fieldKey 字段键
     * @param {string} status 状态
     * @param {Object} details 详细信息
     */
    updateFieldStatus(fieldKey, status, details = {}) {
        if (!this.progressState.isActive) return;
        
        const fieldElement = document.querySelector(`[data-field="${fieldKey}"]`);
        if (!fieldElement) return;
        
        const statusConfig = this.fieldStates[status] || this.fieldStates.pending;
        
        // 更新字段状态图标
        const statusIcon = fieldElement.querySelector('.status-icon, .field-status-icon');
        if (statusIcon) {
            statusIcon.textContent = statusConfig.icon;
            statusIcon.style.color = statusConfig.color;
        }
        
        // 更新字段进度条
        const progressFill = fieldElement.querySelector('.mini-progress-fill');
        if (progressFill) {
            const progressWidth = status === 'success' ? '100%' : 
                                 status === 'failed' ? '100%' : 
                                 status === 'processing' ? '50%' : '0%';
            progressFill.style.width = progressWidth;
            progressFill.style.backgroundColor = statusConfig.color;
        }
        
        // 更新字段值
        if (details.value && status === 'success') {
            const fieldValue = fieldElement.querySelector('.field-value');
            if (fieldValue) {
                fieldValue.textContent = details.value;
                fieldValue.style.color = statusConfig.color;
            }
        }
        
        // 添加状态类
        fieldElement.className = `field-item status-${status}`;
        
        // 记录字段历史
        this.recordFieldHistory(fieldKey, status, details);
        
        // 更新整体进度
        this.updateOverallProgress();
        
        // 添加动画效果
        this.addFieldAnimation(fieldElement, status);
        
        console.log(`📝 字段状态更新: ${fieldKey} -> ${status}`);
    }
    
    /**
     * 更新整体进度
     */
    updateOverallProgress() {
        // 计算进度统计
        const completed = document.querySelectorAll('.field-item.status-success').length;
        const failed = document.querySelectorAll('.field-item.status-failed').length;
        const skipped = document.querySelectorAll('.field-item.status-skipped').length;
        const total = this.progressState.totalSteps;
        const processed = completed + failed + skipped;
        const remaining = total - processed;
        const percentage = total > 0 ? Math.round((processed / total) * 100) : 0;
        
        // 更新状态
        this.progressState.completedSteps = completed;
        this.progressState.failedSteps = failed;
        this.progressState.skippedSteps = skipped;
        this.progressState.currentStep = processed;
        
        // 更新UI元素
        this.updateProgressBar(percentage);
        this.updateStatistics(completed, failed, remaining);
        this.updateStatusText(processed, total);
        this.updateTimeEstimate(remaining);
        
        // 检查是否完成
        if (processed >= total) {
            this.completeProgress();
        }
    }
    
    /**
     * 更新进度条
     */
    updateProgressBar(percentage) {
        const progressFill = document.querySelector('.progress-fill');
        const progressText = document.querySelector('.progress-text');
        
        if (progressFill) {
            progressFill.style.width = `${percentage}%`;
            
            // 根据进度改变颜色
            if (percentage >= 100) {
                progressFill.style.background = 'linear-gradient(90deg, #28a745, #20c997)';
            } else if (percentage >= 75) {
                progressFill.style.background = 'linear-gradient(90deg, #007bff, #0056b3)';
            } else {
                progressFill.style.background = 'linear-gradient(90deg, #17a2b8, #138496)';
            }
        }
        
        if (progressText) {
            progressText.textContent = `${percentage}%`;
        }
        
        // 更新侧边栏进度
        const sidepanelPercentage = document.querySelector('.sidepanel-progress .progress-percentage');
        if (sidepanelPercentage) {
            sidepanelPercentage.textContent = `${percentage}%`;
        }
        
        const sidepanelFill = document.querySelector('.sidepanel-progress .progress-fill');
        if (sidepanelFill) {
            sidepanelFill.style.width = `${percentage}%`;
        }
    }
    
    /**
     * 更新统计信息
     */
    updateStatistics(completed, failed, remaining) {
        const completedElement = document.getElementById('completedCount');
        const failedElement = document.getElementById('failedCount');
        const remainingElement = document.getElementById('remainingCount');
        
        if (completedElement) completedElement.textContent = completed;
        if (failedElement) failedElement.textContent = failed;
        if (remainingElement) remainingElement.textContent = remaining;
        
        // 更新侧边栏计数器
        const fieldCounter = document.querySelector('.field-counter');
        if (fieldCounter) {
            fieldCounter.textContent = `${completed + failed} / ${this.progressState.totalSteps}`;
        }
    }
    
    /**
     * 更新状态文本
     */
    updateStatusText(processed, total) {
        const statusElement = document.getElementById('statusText') || 
                            document.querySelector('.status-text');
        
        if (!statusElement) return;
        
        let statusText = '';
        
        if (processed === 0) {
            statusText = '准备开始填充...';
        } else if (processed < total) {
            statusText = `正在处理字段 ${processed + 1} / ${total}`;
        } else {
            const success = this.progressState.completedSteps;
            const failed = this.progressState.failedSteps;
            
            if (failed === 0) {
                statusText = '🎉 所有字段填充成功！';
            } else if (success > 0) {
                statusText = `⚠️ 填充完成，${failed}个字段失败`;
            } else {
                statusText = '❌ 填充失败';
            }
        }
        
        statusElement.textContent = statusText;
    }
    
    /**
     * 更新时间估算
     */
    updateTimeEstimate(remainingFields) {
        if (!this.config.showTimeEstimate) return;
        
        const timeElement = document.getElementById('timeEstimate');
        if (!timeElement) return;
        
        if (remainingFields <= 0) {
            timeElement.textContent = '已完成';
            return;
        }
        
        const estimatedTime = remainingFields * this.performanceStats.averageFieldTime;
        timeElement.textContent = `预计剩余: ${this.formatTime(estimatedTime)}`;
    }

    /**
     * 完成进度
     */
    completeProgress() {
        this.progressState.isActive = false;

        const duration = Date.now() - this.progressState.startTime;
        const success = this.progressState.completedSteps;
        const failed = this.progressState.failedSteps;
        const total = this.progressState.totalSteps;

        console.log(`✅ 进度完成: ${success}/${total} 成功, ${failed} 失败, 耗时 ${this.formatTime(duration)}`);

        // 更新性能统计
        this.updatePerformanceStats(duration, total);

        // 显示完成动画
        this.showCompletionAnimation(success, failed, total);

        // 自动关闭（可选）
        if (this.config.autoClose) {
            setTimeout(() => {
                this.closeProgress();
            }, 3000);
        }
    }

    /**
     * 显示完成动画
     */
    showCompletionAnimation(success, failed, total) {
        const container = this.elements.container;
        if (!container) return;

        // 添加完成状态类
        container.classList.add('completed');

        // 创建完成提示
        const completionBanner = document.createElement('div');
        completionBanner.className = 'completion-banner';

        if (failed === 0) {
            completionBanner.innerHTML = `
                <div class="completion-icon success">🎉</div>
                <div class="completion-text">
                    <h4>填充完成！</h4>
                    <p>成功填充 ${success} 个字段</p>
                </div>
            `;
        } else {
            completionBanner.innerHTML = `
                <div class="completion-icon warning">⚠️</div>
                <div class="completion-text">
                    <h4>填充完成</h4>
                    <p>成功 ${success} 个，失败 ${failed} 个</p>
                </div>
            `;
        }

        // 插入完成横幅
        const progressContent = container.querySelector('.progress-content');
        if (progressContent) {
            progressContent.insertBefore(completionBanner, progressContent.firstChild);
        }

        // 添加动画
        setTimeout(() => {
            completionBanner.classList.add('show');
        }, 100);
    }

    /**
     * 添加字段动画
     */
    addFieldAnimation(fieldElement, status) {
        if (!fieldElement) return;

        // 移除现有动画类
        fieldElement.classList.remove('field-updating', 'field-success', 'field-failed');

        // 添加新动画类
        fieldElement.classList.add('field-updating');

        setTimeout(() => {
            fieldElement.classList.remove('field-updating');
            fieldElement.classList.add(`field-${status}`);
        }, 200);
    }

    /**
     * 记录字段历史
     */
    recordFieldHistory(fieldKey, status, details) {
        const historyEntry = {
            fieldKey,
            status,
            timestamp: Date.now(),
            duration: details.duration || 0,
            value: details.value || '',
            error: details.error || null
        };

        this.fieldHistory.push(historyEntry);

        // 限制历史记录大小
        if (this.fieldHistory.length > 100) {
            this.fieldHistory = this.fieldHistory.slice(-100);
        }
    }

    /**
     * 更新性能统计
     */
    updatePerformanceStats(totalDuration, totalFields) {
        if (totalFields > 0) {
            const avgTime = totalDuration / totalFields;
            this.performanceStats.averageFieldTime =
                (this.performanceStats.averageFieldTime + avgTime) / 2;
        }

        this.performanceStats.totalProcessed += totalFields;
        this.performanceStats.successRate =
            this.progressState.completedSteps / this.progressState.totalSteps;
    }

    /**
     * 暂停进度
     */
    pauseProgress() {
        // 实现暂停逻辑
        console.log('⏸️ 进度已暂停');
    }

    /**
     * 取消进度
     */
    cancelProgress() {
        if (confirm('确定要取消当前操作吗？')) {
            this.progressState.isActive = false;
            this.closeProgress();
            console.log('⏹️ 进度已取消');
        }
    }

    /**
     * 关闭进度显示
     */
    closeProgress() {
        const container = this.elements.container;
        if (container) {
            container.classList.add('closing');
            setTimeout(() => {
                container.remove();
            }, 300);
        }

        this.resetProgress();
        console.log('❌ 进度显示已关闭');
    }

    /**
     * 切换最小化
     */
    toggleMinimize() {
        const container = this.elements.container;
        if (container) {
            container.classList.toggle('minimized');
        }
    }

    /**
     * 切换字段详情
     */
    toggleFieldDetails() {
        const fieldList = document.getElementById('fieldList');
        const toggleIcon = document.querySelector('.toggle-icon');

        if (fieldList && toggleIcon) {
            const isVisible = fieldList.style.display !== 'none';
            fieldList.style.display = isVisible ? 'none' : 'block';
            toggleIcon.textContent = isVisible ? '🔽' : '🔼';
        }
    }

    /**
     * 重置进度状态
     */
    resetProgress() {
        this.progressState = {
            isActive: false,
            startTime: null,
            currentStep: 0,
            totalSteps: 0,
            completedSteps: 0,
            failedSteps: 0,
            skippedSteps: 0,
            currentField: null,
            estimatedTimeRemaining: 0
        };

        this.fieldHistory = [];
        this.elements = {
            container: null,
            progressBar: null,
            statusText: null,
            fieldList: null,
            timeEstimate: null,
            actionButtons: null
        };
    }

    /**
     * 移除现有进度显示
     */
    removeExistingProgress() {
        const existing = document.getElementById('mdac-progress-visualizer');
        if (existing) {
            existing.remove();
        }

        const sidepanelProgress = document.querySelector('.sidepanel-progress');
        if (sidepanelProgress) {
            sidepanelProgress.remove();
        }
    }

    /**
     * 设置进度位置
     */
    setProgressPosition(container, position) {
        switch (position) {
            case 'top-right':
                container.style.position = 'fixed';
                container.style.top = '20px';
                container.style.right = '20px';
                container.style.zIndex = '10005';
                break;
            case 'bottom-right':
                container.style.position = 'fixed';
                container.style.bottom = '20px';
                container.style.right = '20px';
                container.style.zIndex = '10005';
                break;
            case 'center':
                container.style.position = 'fixed';
                container.style.top = '50%';
                container.style.left = '50%';
                container.style.transform = 'translate(-50%, -50%)';
                container.style.zIndex = '10005';
                break;
            default: // overlay
                container.style.position = 'fixed';
                container.style.top = '0';
                container.style.left = '0';
                container.style.width = '100%';
                container.style.height = '100%';
                container.style.zIndex = '10005';
                container.style.background = 'rgba(0, 0, 0, 0.5)';
                container.style.display = 'flex';
                container.style.alignItems = 'center';
                container.style.justifyContent = 'center';
        }
    }

    /**
     * 缓存元素引用
     */
    cacheElements(container) {
        this.elements.container = container;
        this.elements.progressBar = container.querySelector('.progress-bar');
        this.elements.statusText = container.querySelector('.status-text');
        this.elements.fieldList = container.querySelector('.field-list');
        this.elements.timeEstimate = container.querySelector('.time-estimate');
        this.elements.actionButtons = container.querySelector('.progress-actions');
    }

    /**
     * 计算预估时间
     */
    calculateEstimatedTime(totalFields) {
        return totalFields * this.performanceStats.averageFieldTime;
    }

    /**
     * 格式化时间
     */
    formatTime(milliseconds) {
        if (milliseconds < 1000) {
            return `${Math.round(milliseconds)}ms`;
        } else if (milliseconds < 60000) {
            return `${Math.round(milliseconds / 1000)}s`;
        } else {
            const minutes = Math.floor(milliseconds / 60000);
            const seconds = Math.round((milliseconds % 60000) / 1000);
            return `${minutes}m ${seconds}s`;
        }
    }

    /**
     * 获取字段标签
     */
    getFieldLabel(fieldKey) {
        const labels = {
            name: '姓名',
            passportNo: '护照号码',
            dateOfBirth: '出生日期',
            nationality: '国籍',
            sex: '性别',
            passportExpiry: '护照到期日',
            email: '电子邮箱',
            confirmEmail: '确认邮箱',
            countryCode: '国家代码',
            mobileNo: '手机号码',
            arrivalDate: '到达日期',
            departureDate: '离开日期',
            flightNo: '航班号',
            modeOfTravel: '旅行方式',
            lastPort: '最后港口',
            accommodation: '住宿类型',
            address: '地址',
            address2: '地址2',
            state: '州/省',
            postcode: '邮政编码',
            city: '城市'
        };
        return labels[fieldKey] || fieldKey;
    }

    /**
     * 加载配置
     */
    loadConfig() {
        try {
            const savedConfig = localStorage.getItem('mdac-progress-config');
            if (savedConfig) {
                this.config = { ...this.config, ...JSON.parse(savedConfig) };
            }
        } catch (error) {
            console.warn('加载进度配置失败:', error);
        }
    }

    /**
     * 保存配置
     */
    saveConfig() {
        try {
            localStorage.setItem('mdac-progress-config', JSON.stringify(this.config));
        } catch (error) {
            console.warn('保存进度配置失败:', error);
        }
    }

    /**
     * 设置样式
     */
    setupStyles() {
        if (document.getElementById('progress-visualizer-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'progress-visualizer-styles';
        styles.textContent = `
            .progress-visualizer {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                opacity: 0;
                transition: opacity 0.3s ease;
            }

            .progress-visualizer.show {
                opacity: 1;
            }

            .progress-container {
                background: white;
                border-radius: 12px;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
                max-width: 500px;
                width: 90%;
                max-height: 80vh;
                overflow: hidden;
                animation: progressSlideIn 0.3s ease-out;
            }

            @keyframes progressSlideIn {
                from {
                    opacity: 0;
                    transform: scale(0.9) translateY(-20px);
                }
                to {
                    opacity: 1;
                    transform: scale(1) translateY(0);
                }
            }

            .progress-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 16px 20px;
                background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
                color: white;
            }

            .progress-title {
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .progress-title h3 {
                margin: 0;
                font-size: 16px;
                font-weight: 600;
            }

            .progress-controls {
                display: flex;
                gap: 8px;
            }

            .control-btn {
                background: rgba(255, 255, 255, 0.2);
                border: none;
                border-radius: 4px;
                color: white;
                cursor: pointer;
                padding: 4px 8px;
                transition: background 0.2s ease;
            }

            .control-btn:hover {
                background: rgba(255, 255, 255, 0.3);
            }
        `;

        document.head.appendChild(styles);
    }

    /**
     * 获取进度统计
     */
    getProgressStats() {
        return {
            ...this.progressState,
            fieldHistory: this.fieldHistory.slice(-10),
            performanceStats: this.performanceStats
        };
    }
}

// 导出类供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ProgressVisualizer;
} else {
    window.ProgressVisualizer = ProgressVisualizer;
}
