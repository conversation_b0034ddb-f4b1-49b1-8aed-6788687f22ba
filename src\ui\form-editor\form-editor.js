/**
 * MDAC AI智能填充工具 - 表单编辑器脚本
 * 处理表单编辑器的智能解析和表单管理功能
 */

class MDACFormEditor {
    constructor() {
        this.formData = {};
        this.isParsingContent = false;
        this.init();
    }

    /**
     * 初始化表单编辑器
     */
    async init() {
        this.setupEventListeners();
        this.loadURLData();
        this.updateAISuggestions('🤖 AI助手已就绪！可以使用智能解析功能提取表单数据。');
        console.log('MDAC表单编辑器已初始化');
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 智能解析相关事件
        document.getElementById('contentParseBtn').addEventListener('click', () => this.showContentParser());
        document.getElementById('closeParsePanelBtn').addEventListener('click', () => this.hideContentParser());
        document.getElementById('startParseBtn').addEventListener('click', () => this.startContentParse());
        document.getElementById('clearParseInputBtn').addEventListener('click', () => this.clearParseInput());

        // 其他快速操作
        document.getElementById('clearFormBtn').addEventListener('click', () => this.clearForm());

        // AI助手功能
        document.getElementById('aiValidateBtn').addEventListener('click', () => this.validateWithAI());
        document.getElementById('aiOptimizeBtn').addEventListener('click', () => this.optimizeWithAI());
        document.getElementById('aiTranslateBtn').addEventListener('click', () => this.translateWithAI());

        // 表单操作
        document.getElementById('resetBtn').addEventListener('click', () => this.resetForm());
        document.getElementById('previewBtn').addEventListener('click', () => this.previewData());
        document.getElementById('submitBtn').addEventListener('click', () => this.submitForm());

        // 自动保存功能
        document.getElementById('autoSaveBtn').addEventListener('click', () => this.toggleAutoSave());
        document.getElementById('helpBtn').addEventListener('click', () => this.showHelp());
    }

    /**
     * 从URL加载数据
     */
    loadURLData() {
        const urlParams = new URLSearchParams(window.location.search);
        const dataParam = urlParams.get('data');
        
        if (dataParam) {
            try {
                const data = JSON.parse(decodeURIComponent(dataParam));
                this.fillFormWithData(data);
                this.updateAISuggestions('✅ 已从解析结果加载数据到表单中。');
            } catch (error) {
                console.error('加载URL数据失败:', error);
            }
        }
    }

    /**
     * 显示智能内容解析面板
     */
    showContentParser() {
        const panel = document.getElementById('contentParserPanel');
        if (panel) {
            panel.style.display = 'block';
            document.getElementById('contentParseInput').focus();
            this.updateAISuggestions('📝 请在解析面板中粘贴任意格式的内容，AI将自动提取表单数据。');
        }
    }

    /**
     * 隐藏智能内容解析面板
     */
    hideContentParser() {
        const panel = document.getElementById('contentParserPanel');
        if (panel) {
            panel.style.display = 'none';
            this.clearParseInput();
            this.hideParseStatus();
        }
    }

    /**
     * 清空解析输入
     */
    clearParseInput() {
        const input = document.getElementById('contentParseInput');
        if (input) {
            input.value = '';
        }
    }

    /**
     * 开始内容解析
     */
    async startContentParse() {
        const input = document.getElementById('contentParseInput');
        const content = input.value.trim();
        
        if (!content) {
            this.showStatusMessage('请先输入要解析的内容', 'error');
            return;
        }

        if (this.isParsingContent) {
            return; // 防止重复解析
        }

        this.isParsingContent = true;
        this.showParseStatus();
        this.updateParseButton(true);

        try {
            // 调用Chrome扩展的AI解析功能
            const response = await chrome.runtime.sendMessage({
                action: 'callGeminiAI',
                prompt: this.buildContentParsePrompt(content),
                context: '你是一个专业的信息提取专家，专门从文本中提取结构化数据。'
            });

            if (response.success) {
                await this.processParseResult(response.data);
                this.updateAISuggestions('✅ 内容解析完成！已自动填充到表单中。');
            } else {
                throw new Error('AI解析失败');
            }
        } catch (error) {
            console.error('内容解析失败:', error);
            this.showStatusMessage('解析失败: ' + error.message, 'error');
            this.updateAISuggestions('❌ 内容解析失败，请检查网络连接或重试。');
        } finally {
            this.isParsingContent = false;
            this.hideParseStatus();
            this.updateParseButton(false);
        }
    }

    /**
     * 构建内容解析提示词
     */
    buildContentParsePrompt(content) {
        return `请从以下文本中提取MDAC表单所需的信息：

文本内容：
${content}

请提取以下字段（如果存在）：
- name: 姓名（英文全名）
- passportNo: 护照号码
- dateOfBirth: 出生日期（DD/MM/YYYY格式）
- nationality: 国籍（3位代码，如CHN、USA等）
- sex: 性别（1=男性，2=女性）
- passportExpiry: 护照到期日期（DD/MM/YYYY格式）
- email: 电子邮箱
- countryCode: 国家代码（如+86、+60等）
- mobileNo: 手机号码（不含国家代码）
- arrivalDate: 到达日期（DD/MM/YYYY格式）
- departureDate: 离开日期（DD/MM/YYYY格式）
- flightNo: 航班号或交通工具编号
- modeOfTravel: 旅行方式（AIR=航空，LAND=陆路，SEA=海路）
- lastPort: 最后港口（3位代码）
- accommodation: 住宿类型（01=酒店，02=朋友家，99=其他）
- address: 马来西亚地址行1（英文）
- address2: 马来西亚地址行2（英文，可选）
- state: 州代码（如14=吉隆坡）
- postcode: 邮政编码（5位数字）
- city: 城市代码

请只返回JSON格式的数据，不要包含其他说明。如果某个字段无法确定，请设为null。
对于中文信息，请自动翻译为英文。确保日期格式为DD/MM/YYYY。`;
    }

    /**
     * 显示解析状态
     */
    showParseStatus() {
        const status = document.getElementById('parseStatus');
        const progressBar = document.getElementById('parseProgressBar');
        
        if (status) {
            status.style.display = 'block';
            
            // 模拟进度条
            let progress = 0;
            this.progressInterval = setInterval(() => {
                progress += Math.random() * 10;
                if (progress > 90) progress = 90;
                
                if (progressBar) {
                    progressBar.style.width = progress + '%';
                }
            }, 300);
        }
    }

    /**
     * 隐藏解析状态
     */
    hideParseStatus() {
        const status = document.getElementById('parseStatus');
        if (status) {
            status.style.display = 'none';
        }
        
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
            this.progressInterval = null;
        }
    }

    /**
     * 更新解析按钮状态
     */
    updateParseButton(isLoading) {
        const btn = document.getElementById('startParseBtn');
        const textSpan = btn.querySelector('.parse-btn-text');
        const loadingSpan = btn.querySelector('.parse-btn-loading');
        
        if (isLoading) {
            textSpan.style.display = 'none';
            loadingSpan.style.display = 'inline';
            btn.disabled = true;
        } else {
            textSpan.style.display = 'inline';
            loadingSpan.style.display = 'none';
            btn.disabled = false;
        }
    }

    /**
     * 处理解析结果
     */
    async processParseResult(aiResponse) {
        try {
            // 清理AI返回的JSON
            const cleanResult = aiResponse.replace(/```json|```/g, '').trim();
            const extractedData = JSON.parse(cleanResult);
            
            // 填充表单
            this.fillFormWithData(extractedData);
            
            // 隐藏解析面板
            this.hideContentParser();
            
            // 更新状态
            this.showStatusMessage('解析完成，已填充表单', 'success');
            
        } catch (parseError) {
            console.error('解析AI返回的JSON失败:', parseError);
            this.showStatusMessage('AI解析结果格式异常', 'error');
        }
    }

    /**
     * 使用数据填充表单
     */
    fillFormWithData(data) {
        Object.keys(data).forEach(fieldId => {
            const value = data[fieldId];
            if (value) {
                const field = document.getElementById(fieldId);
                if (field) {
                    field.value = value;
                    field.dispatchEvent(new Event('change', { bubbles: true }));
                }
            }
        });
        
        this.formData = { ...data };
        this.updateFieldCount();
    }

    /**
     * 更新AI建议显示
     */
    updateAISuggestions(message) {
        const suggestions = document.getElementById('aiSuggestions');
        if (suggestions) {
            suggestions.innerHTML = message;
        }
    }

    /**
     * 显示状态消息
     */
    showStatusMessage(message, type = 'info') {
        const statusText = document.getElementById('statusText');
        if (statusText) {
            statusText.textContent = message;
            statusText.className = `status-text ${type}`;
            
            // 3秒后恢复默认状态
            setTimeout(() => {
                statusText.textContent = '表单就绪';
                statusText.className = 'status-text';
            }, 3000);
        }
    }

    /**
     * 更新字段计数
     */
    updateFieldCount() {
        const fieldCount = document.getElementById('fieldCount');
        if (fieldCount) {
            const filledFields = Object.keys(this.formData).filter(key => this.formData[key]).length;
            fieldCount.textContent = `已填写: ${filledFields}/21`;
        }
    }

    /**
     * 清空表单
     */
    clearForm() {
        // 清空所有表单字段
        const form = document.getElementById('mdacForm');
        if (form) {
            form.reset();
            this.formData = {};
            this.updateFieldCount();
            this.updateAISuggestions('🗑️ 表单已清空，可以重新开始填写。');
        }
    }

    /**
     * AI验证功能
     */
    validateWithAI() {
        this.updateAISuggestions('✅ AI验证功能：使用智能解析面板进行内容验证。');
    }

    /**
     * AI优化功能
     */
    optimizeWithAI() {
        this.updateAISuggestions('💡 AI优化功能：解析内容后会自动提供优化建议。');
    }

    /**
     * AI翻译功能
     */
    translateWithAI() {
        this.updateAISuggestions('🌐 AI翻译功能：智能解析会自动翻译中文内容。');
    }

    /**
     * 其他功能占位符
     */
    resetForm() { this.clearForm(); }
    previewData() { this.updateAISuggestions('👁️ 数据预览：查看当前表单数据。'); }
    submitForm() { this.updateAISuggestions('📤 表单提交：请在MDAC官网提交表单。'); }
    toggleAutoSave() { this.updateAISuggestions('💾 自动保存：数据会自动保存到浏览器。'); }
    showHelp() { this.updateAISuggestions('❓ 帮助：使用智能解析功能快速填写表单。'); }
}

// 初始化表单编辑器
document.addEventListener('DOMContentLoaded', () => {
    new MDACFormEditor();
});
