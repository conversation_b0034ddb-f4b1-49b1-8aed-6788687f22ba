/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    color: #333;
    line-height: 1.6;
    min-height: 100vh;
}

.container {
    max-width: 1000px;
    margin: 0 auto;
    background: white;
    min-height: 100vh;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo img {
    width: 48px;
    height: 48px;
}

.logo h1 {
    font-size: 24px;
    font-weight: 600;
}

.version {
    background: rgba(255,255,255,0.2);
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 12px;
}

/* 导航标签 */
.nav-tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.nav-tab {
    flex: 1;
    padding: 15px 20px;
    border: none;
    background: none;
    cursor: pointer;
    font-size: 14px;
    color: #666;
    transition: all 0.2s ease;
    border-bottom: 3px solid transparent;
}

.nav-tab:hover {
    background: #e9ecef;
    color: #333;
}

.nav-tab.active {
    background: white;
    color: #667eea;
    border-bottom-color: #667eea;
}

/* 内容区域 */
.content {
    padding: 30px;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 设置区块 */
.section {
    margin-bottom: 40px;
    background: #f8f9fa;
    border-radius: 12px;
    padding: 25px;
    border: 1px solid #e9ecef;
}

.section h2 {
    font-size: 18px;
    margin-bottom: 20px;
    color: #333;
    border-bottom: 2px solid #667eea;
    padding-bottom: 8px;
}

/* 设置项 */
.setting-item {
    margin-bottom: 25px;
}

.setting-item label {
    display: block;
    font-weight: 500;
    margin-bottom: 8px;
    color: #333;
}

.setting-item input[type="text"],
.setting-item input[type="email"],
.setting-item input[type="tel"],
.setting-item input[type="password"],
.setting-item input[type="date"],
.setting-item select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.setting-item input:focus,
.setting-item select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.description {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
    line-height: 1.4;
}

/* 复选框样式 */
.checkbox-label {
    display: flex !important;
    align-items: center;
    cursor: pointer;
    font-weight: normal !important;
    margin-bottom: 0 !important;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    background: white;
    border: 2px solid #ddd;
    border-radius: 4px;
    margin-right: 10px;
    position: relative;
    transition: all 0.2s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: #667eea;
    border-color: #667eea;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '';
    position: absolute;
    left: 6px;
    top: 2px;
    width: 4px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* 范围滑块 */
input[type="range"] {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #ddd;
    outline: none;
    -webkit-appearance: none;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
}

input[type="range"]::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
    border: none;
}

.range-labels {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}

/* 输入组 */
.input-group {
    display: flex;
    align-items: center;
}

.input-group input {
    flex: 1;
    border-radius: 6px 0 0 6px;
}

.toggle-btn {
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-left: none;
    border-radius: 0 6px 6px 0;
    background: #f8f9fa;
    cursor: pointer;
    transition: background 0.2s ease;
}

.toggle-btn:hover {
    background: #e9ecef;
}

/* 按钮样式 */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn.primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn.secondary {
    background: #f8f9fa;
    color: #666;
    border: 1px solid #dee2e6;
}

.btn.danger {
    background: #dc3545;
    color: white;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.btn:active {
    transform: translateY(0);
}

/* 测试结果 */
.test-result {
    margin-top: 10px;
    padding: 10px;
    border-radius: 6px;
    font-size: 13px;
    display: none;
}

.test-result.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.test-result.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* 表单样式 */
.template-form {
    background: white;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.form-group {
    flex: 1;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #333;
}

.form-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

/* 数据统计 */
.data-stats {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.stat-item {
    flex: 1;
    background: white;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    border: 1px solid #e9ecef;
}

.stat-number {
    font-size: 24px;
    font-weight: 600;
    color: #667eea;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 12px;
    color: #666;
}

.data-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* 关于页面 */
.about-info {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
}

.about-item {
    margin-bottom: 10px;
    font-size: 14px;
}

.feature-list {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
}

.feature-list h3 {
    margin-bottom: 15px;
    color: #333;
}

.feature-list ul {
    list-style: none;
    padding: 0;
}

.feature-list li {
    padding: 5px 0;
    font-size: 14px;
}

.links {
    background: white;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.links h3 {
    margin-bottom: 15px;
    color: #333;
}

.link-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* 底部操作 */
.footer {
    background: #f8f9fa;
    padding: 20px 30px;
    border-top: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.status {
    font-size: 14px;
    color: #666;
}

.status.success {
    color: #28a745;
}

.status.error {
    color: #dc3545;
}

.actions {
    display: flex;
    gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        margin: 0;
    }
    
    .header {
        padding: 20px;
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .content {
        padding: 20px;
    }
    
    .section {
        padding: 20px;
    }
    
    .form-row {
        flex-direction: column;
        gap: 10px;
    }
    
    .data-stats {
        flex-direction: column;
        gap: 10px;
    }
    
    .footer {
        padding: 15px 20px;
        flex-direction: column;
        gap: 15px;
    }
    
    .actions {
        width: 100%;
        justify-content: center;
    }
}
