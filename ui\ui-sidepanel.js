/**
 * MDAC AI智能填充工具 - 侧边栏版本
 * 处理扩展侧边栏的用户界面和交互逻辑
 */

class MDACAssistantSidePanel {
    constructor() {
        console.log('🚀 MDACAssistantSidePanel 构造函数开始执行');
        this.currentTab = null;
        this.isMDACPage = false;
        this.aiStatus = 'ready';
        this.parsedData = null;
        this.supplementData = null; // 持久化的补充信息
        this.mergedData = null; // 合并后的完整数据
        this.dataPreviewManager = null;
        this.errorRecoveryManager = null;
        this.fillMonitor = null;
        this.confidenceEvaluator = null;
        this.progressVisualizer = null;
        this.connectionCheckInterval = null;

        console.log('🚀 开始初始化侧边栏');
        this.initializeSidePanel();
        console.log('🚀 MDACAssistantSidePanel 构造函数完成');
    }

    /**
     * 侧边栏特定初始化
     */
    async initializeSidePanel() {
        console.log('🚀 MDAC AI侧边栏正在初始化...');
        
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.init());
        } else {
            await this.init();
        }
        
        // 侧边栏特定的事件监听
        this.setupSidePanelEvents();
        
        // 开始定期检查连接状态
        this.startConnectionMonitoring();
    }

    /**
     * 初始化主要功能
     */
    async init() {
        console.log('🔧 init 方法开始执行');
        console.log('🔧 检查 callGeminiAPI 方法:', typeof this.callGeminiAPI);
        
        await this.getCurrentTab();
        await this.detectMDACPage();
        this.setupEventListeners();
        this.initializeEnhancedTools();
        await this.loadUserSettings();
        this.initializeDataPreviewManager();
        this.initializeErrorRecoveryManager();
        this.initializeFillMonitor();
        this.initializeConfidenceEvaluator();
        this.initializeProgressVisualizer();
        this.initializeSupplementInput();
        this.updateUI();
        await this.testAIConnection();
        
        console.log('🔧 init 方法完成，检查 callGeminiAPI 方法:', typeof this.callGeminiAPI);
    }

    /**
     * 设置侧边栏特定事件
     */
    setupSidePanelEvents() {
        // 监听窗口大小变化
        window.addEventListener('resize', () => this.handleResize());
        
        // 监听页面可见性变化
        document.addEventListener('visibilitychange', () => this.handleVisibilityChange());
        
        // 监听侧边栏关闭事件
        window.addEventListener('beforeunload', () => this.handleSidePanelClose());
        
        // 监听标签页切换
        chrome.tabs.onActivated.addListener(() => this.handleTabChange());
        chrome.tabs.onUpdated.addListener(() => this.handleTabChange());
    }

    /**
     * 开始连接监控
     */
    startConnectionMonitoring() {
        // 立即检查一次
        this.checkPageConnection();
        
        // 每5秒检查一次连接状态
        this.connectionCheckInterval = setInterval(() => {
            this.checkPageConnection();
        }, 5000);
    }

    /**
     * 停止连接监控
     */
    stopConnectionMonitoring() {
        if (this.connectionCheckInterval) {
            clearInterval(this.connectionCheckInterval);
            this.connectionCheckInterval = null;
        }
    }

    /**
     * 处理窗口大小变化
     */
    handleResize() {
        // 调整布局以适应新的窗口大小
        this.adjustLayout();
    }

    /**
     * 处理可见性变化
     */
    handleVisibilityChange() {
        if (document.hidden) {
            console.log('📱 侧边栏已隐藏');
            this.stopConnectionMonitoring();
        } else {
            console.log('📱 侧边栏已显示');
            // 重新开始监控
            this.startConnectionMonitoring();
        }
    }

    /**
     * 处理侧边栏关闭
     */
    handleSidePanelClose() {
        console.log('📱 侧边栏正在关闭');
        // 保存当前状态
        this.saveCurrentState();
        // 停止监控
        this.stopConnectionMonitoring();
    }

    /**
     * 初始化增强工具
     */
    initializeEnhancedTools() {
        try {
            // 初始化MDAC验证器
            if (typeof MDACValidator !== 'undefined') {
                this.validator = new MDACValidator();
                console.log('✅ MDAC验证器初始化成功');
            } else {
                console.warn('⚠️ MDACValidator未找到，将使用基础验证');
            }

            // 初始化增强表单填充器
            if (typeof EnhancedFormFiller !== 'undefined') {
                this.enhancedFormFiller = new EnhancedFormFiller();
                console.log('✅ 增强表单填充器初始化成功');
            } else {
                console.warn('⚠️ EnhancedFormFiller未找到，将使用基础填充');
            }

            // 初始化Google Maps集成 - 使用延迟加载避免时序问题
            this.initializeGoogleMaps();

            console.log('✅ 增强工具初始化完成');
        } catch (error) {
            console.error('❌ 增强工具初始化失败:', error);
            this.showMessage('增强工具初始化失败: ' + error.message, 'error');
        }
    }

    /**
     * 初始化Google Maps集成（延迟加载）
     */
    initializeGoogleMaps() {
        // 使用短暂延迟确保所有脚本都已加载
        setTimeout(() => {
            if (typeof GoogleMapsIntegration !== 'undefined') {
                const apiKey = window.MDAC_AI_CONFIG?.GEMINI_CONFIG?.DEFAULT_API_KEY ||
                              (typeof GEMINI_CONFIG !== 'undefined' ? GEMINI_CONFIG.DEFAULT_API_KEY : null);
                if (apiKey) {
                    this.googleMaps = new GoogleMapsIntegration(apiKey);
                    console.log('✅ Google Maps集成初始化成功');
                } else {
                    console.warn('⚠️ API密钥未找到，Google Maps功能不可用');
                }
            } else {
                console.warn('⚠️ GoogleMapsIntegration未找到，地址标准化功能不可用');
            }
        }, 100);
    }

    /**
     * 处理标签页变化
     */
    async handleTabChange() {
        await this.getCurrentTab();
        await this.detectMDACPage();
        this.checkPageConnection();
    }

    /**
     * 调整布局
     */
    adjustLayout() {
        const container = document.querySelector('.sidepanel-container');
        if (container) {
            const width = window.innerWidth;
            if (width < 350) {
                container.classList.add('compact-mode');
            } else {
                container.classList.remove('compact-mode');
            }
        }
    }

    /**
     * 检查与页面的连接状态
     */
    async checkPageConnection() {
        try {
            const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
            const activeTab = tabs[0];
            
            if (activeTab && activeTab.url && activeTab.url.includes('imigresen-online.imi.gov.my')) {
                this.updateConnectionStatus('connected');
                this.isMDACPage = true;
            } else {
                this.updateConnectionStatus('disconnected');
                this.isMDACPage = false;
            }
            
            // 更新UI状态
            this.updateUI();
        } catch (error) {
            console.error('检查页面连接失败:', error);
            this.updateConnectionStatus('error');
        }
    }

    /**
     * 更新连接状态显示
     */
    updateConnectionStatus(status) {
        const statusElement = document.querySelector('#connectionStatus');
        if (statusElement) {
            statusElement.className = `connection-status ${status}`;
            statusElement.textContent = this.getConnectionStatusText(status);
        }
    }

    /**
     * 获取连接状态文本
     */
    getConnectionStatusText(status) {
        const statusTexts = {
            connected: '🟢 已连接到MDAC网站',
            disconnected: '🟡 请打开MDAC网站使用完整功能',
            error: '🔴 连接检测异常'
        };
        return statusTexts[status] || '🟡 状态未知';
    }

    /**
     * 保存当前状态
     */
    saveCurrentState() {
        try {
            const state = {
                parsedData: this.parsedData,
                supplementData: this.supplementData,
                mergedData: this.mergedData,
                timestamp: Date.now()
            };
            
            chrome.storage.local.set({ 'mdac_sidepanel_state': state }, () => {
                console.log('侧边栏状态已保存');
            });
        } catch (error) {
            console.error('保存状态失败:', error);
        }
    }

    /**
     * 恢复保存的状态
     */
    async restoreSavedState() {
        try {
            const result = await chrome.storage.local.get(['mdac_sidepanel_state']);
            const state = result.mdac_sidepanel_state;
            
            if (state && state.timestamp) {
                // 检查状态是否过期（24小时）
                const now = Date.now();
                const maxAge = 24 * 60 * 60 * 1000; // 24小时
                
                if (now - state.timestamp < maxAge) {
                    this.parsedData = state.parsedData;
                    this.supplementData = state.supplementData;
                    this.mergedData = state.mergedData;
                    console.log('已恢复侧边栏状态');
                    return true;
                }
            }
        } catch (error) {
            console.error('恢复状态失败:', error);
        }
        return false;
    }

    /**
     * 获取当前标签页
     */
    async getCurrentTab() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            this.currentTab = tab;
        } catch (error) {
            console.error('获取当前标签页失败:', error);
        }
    }

    /**
     * 检测是否为MDAC页面
     */
    async detectMDACPage() {
        if (!this.currentTab || !this.currentTab.url) {
            this.isMDACPage = false;
            return;
        }

        this.isMDACPage = this.currentTab.url.includes('imigresen-online.imi.gov.my');
        
        if (this.isMDACPage) {
            console.log('✅ 检测到MDAC网站');
            this.updateDetectionStatus('detected', '✅ 已检测到MDAC网站');
        } else {
            console.log('⚠️ 当前不在MDAC网站');
            this.updateDetectionStatus('not-detected', '⚠️ 请打开MDAC网站');
        }
    }

    /**
     * 更新检测状态显示
     */
    updateDetectionStatus(status, message) {
        const detectionElement = document.querySelector('#detectionStatus');
        if (detectionElement) {
            const textElement = detectionElement.querySelector('.text');
            if (textElement) {
                textElement.textContent = message;
            }
            
            detectionElement.className = `detection-status ${status}`;
        }
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 新的图片上传按钮
        document.getElementById('imageUploadBtn')?.addEventListener('click', () => this.triggerImageUpload());
        document.getElementById('imageInput')?.addEventListener('change', (e) => this.handleImageUpload(e));

        // 工具栏按钮
        document.getElementById('clearAllBtn')?.addEventListener('click', () => this.clearAllData());
        document.getElementById('previewBtn')?.addEventListener('click', () => this.previewAllData());

        // 个人信息AI解析
        document.getElementById('parsePersonalBtn')?.addEventListener('click', (event) => {
            event.preventDefault();
            this.parsePersonalInfo().catch(error => {
                console.error('解析个人信息时出错:', error);
                this.showMessage('解析失败: ' + error.message, 'error');
            });
        });

        // 旅行信息AI解析
        document.getElementById('parseTravelBtn')?.addEventListener('click', () => this.parseTravelInfo());

        // 旧的解析按钮（保持向后兼容）
        document.getElementById('parseContentBtn')?.addEventListener('click', () => this.parseContent());
        document.getElementById('clearContentBtn')?.addEventListener('click', () => this.clearContent());

        // 补充信息相关
        document.getElementById('supplementInput')?.addEventListener('input', () => this.handleSupplementInput());
        document.getElementById('clearSupplementBtn')?.addEventListener('click', () => this.clearSupplementData());
        document.getElementById('previewMergedDataBtn')?.addEventListener('click', () => this.previewMergedData());

        // 表单填充
        document.getElementById('fillFormBtn')?.addEventListener('click', () => this.fillForm());
        document.getElementById('quickFillBtn')?.addEventListener('click', () => this.quickFill());
        document.getElementById('updateToMDACBtn')?.addEventListener('click', () => this.updateToMDAC());

        // 其他功能
        document.getElementById('openFormBtn')?.addEventListener('click', () => this.openFormEditor());
        document.getElementById('aiSettingsBtn')?.addEventListener('click', () => this.openAISettings());
        document.getElementById('helpBtn')?.addEventListener('click', () => this.showHelp());
        document.getElementById('saveBtn')?.addEventListener('click', () => this.saveData());
        document.getElementById('clearBtn')?.addEventListener('click', () => this.clearAllData());
        document.getElementById('settingsBtn')?.addEventListener('click', () => this.showSettings());

        // 模态框
        document.getElementById('modalClose')?.addEventListener('click', () => this.closeModal());
        document.getElementById('modalCancel')?.addEventListener('click', () => this.closeModal());
        document.getElementById('modalConfirm')?.addEventListener('click', () => this.handleModalConfirm());

        // 预设信息编辑
        document.getElementById('editPresetBtn')?.addEventListener('click', () => this.editPresetInfo());
    }

    /**
     * 加载用户设置
     */
    async loadUserSettings() {
        try {
            const result = await chrome.storage.sync.get(['mdacSettings']);
            this.userSettings = result.mdacSettings || {};
            console.log('用户设置已加载');
        } catch (error) {
            console.error('加载用户设置失败:', error);
            this.userSettings = {};
        }
    }

    /**
     * 初始化数据预览管理器
     */
    initializeDataPreviewManager() {
        try {
            this.dataPreviewManager = new DataPreviewManager();
            console.log('数据预览管理器初始化成功');
        } catch (error) {
            console.error('数据预览管理器初始化失败:', error);
        }
    }

    /**
     * 初始化错误恢复管理器
     */
    initializeErrorRecoveryManager() {
        try {
            this.errorRecoveryManager = new ErrorRecoveryManager();
            console.log('错误恢复管理器初始化成功');
        } catch (error) {
            console.error('错误恢复管理器初始化失败:', error);
        }
    }

    /**
     * 初始化填充监控器
     */
    initializeFillMonitor() {
        try {
            this.fillMonitor = new FillMonitor();
            console.log('填充监控器初始化成功');
        } catch (error) {
            console.error('填充监控器初始化失败:', error);
        }
    }

    /**
     * 初始化置信度评估器
     */
    initializeConfidenceEvaluator() {
        try {
            this.confidenceEvaluator = new ConfidenceEvaluator();
            console.log('✅ 置信度评估器初始化完成');
        } catch (error) {
            console.error('❌ 置信度评估器初始化失败:', error);
        }
    }

    /**
     * 初始化进度可视化器
     */
    initializeProgressVisualizer() {
        try {
            this.progressVisualizer = new ProgressVisualizer();
            console.log('✅ 进度可视化器初始化完成');
        } catch (error) {
            console.error('❌ 进度可视化器初始化失败:', error);
        }
    }

    /**
     * 初始化补充信息输入
     */
    async initializeSupplementInput() {
        try {
            // 加载保存的补充信息
            const result = await chrome.storage.local.get(['mdac_supplement_data']);
            if (result.mdac_supplement_data) {
                this.supplementData = result.mdac_supplement_data;
                
                // 填充到输入框
                const supplementInput = document.getElementById('supplementInput');
                if (supplementInput && this.supplementData.rawText) {
                    supplementInput.value = this.supplementData.rawText;
                }
                
                // 更新状态显示
                this.updateSupplementStatus();
            }
            
            console.log('补充信息输入初始化成功');
        } catch (error) {
            console.error('补充信息输入初始化失败:', error);
        }
    }

    /**
     * 更新UI状态
     */
    updateUI() {
        // 更新AI状态
        this.updateAIStatus();
        
        // 更新按钮状态
        this.updateButtonStates();
        
        // 更新功能可用性
        this.updateFeatureAvailability();
    }

    /**
     * 更新AI状态显示
     */
    updateAIStatus() {
        const statusElement = document.querySelector('#aiStatus');
        if (statusElement) {
            const statusDot = statusElement.querySelector('.status-dot');
            const statusText = statusElement.querySelector('.status-text');
            
            if (statusDot && statusText) {
                switch (this.aiStatus) {
                    case 'ready':
                        statusDot.style.background = '#4caf50';
                        statusText.textContent = 'AI就绪';
                        break;
                    case 'working':
                        statusDot.style.background = '#ff9800';
                        statusText.textContent = 'AI工作中';
                        break;
                    case 'error':
                        statusDot.style.background = '#f44336';
                        statusText.textContent = 'AI异常';
                        break;
                    default:
                        statusDot.style.background = '#9e9e9e';
                        statusText.textContent = '状态未知';
                }
            }
        }
    }

    /**
     * 更新按钮状态
     */
    updateButtonStates() {
        const parseBtn = document.getElementById('parseContentBtn');
        const fillBtn = document.getElementById('fillFormBtn');
        const quickFillBtn = document.getElementById('quickFillBtn');
        
        // 根据页面状态和数据状态更新按钮
        if (parseBtn) {
            parseBtn.disabled = this.aiStatus === 'working';
        }
        
        if (fillBtn) {
            fillBtn.disabled = !this.isMDACPage || (!this.parsedData && !this.supplementData);
        }
        
        if (quickFillBtn) {
            quickFillBtn.disabled = !this.isMDACPage || (!this.parsedData && !this.supplementData);
        }
    }

    /**
     * 更新功能可用性
     */
    updateFeatureAvailability() {
        const mainContent = document.querySelector('#mainContent');
        if (mainContent) {
            if (this.isMDACPage) {
                mainContent.classList.remove('disabled');
            } else {
                // 不完全禁用，但显示提示
                const quickActions = document.querySelector('.quick-actions');
                if (quickActions) {
                    const buttons = quickActions.querySelectorAll('button');
                    buttons.forEach(btn => {
                        if (btn.id === 'quickFillBtn') {
                            btn.disabled = true;
                            btn.title = '请先打开MDAC网站';
                        }
                    });
                }
            }
        }
    }

    /**
     * 测试AI连接
     */
    async testAIConnection() {
        this.aiStatus = 'working';
        this.updateAIStatus();

        try {
            const response = await chrome.runtime.sendMessage({
                action: 'test-ai-connection'
            });

            if (response && response.success) {
                this.aiStatus = 'ready';
                console.log('✅ AI连接测试成功');
            } else {
                throw new Error(response?.error || 'AI连接测试失败');
            }
        } catch (error) {
            this.aiStatus = 'error';
            console.error('❌ AI连接测试失败:', error);
        }

        this.updateAIStatus();
    }

    /**
     * 调用Gemini AI API
     * @param {string} prompt - AI提示词
     * @param {string} context - 上下文信息
     * @returns {Promise<string>} AI响应结果
     */
    async callGeminiAPI(prompt, context = '') {
        try {
            const apiKey = window.MDAC_AI_CONFIG?.GEMINI_CONFIG?.DEFAULT_API_KEY ||
                          GEMINI_CONFIG?.DEFAULT_API_KEY;
            
            if (!apiKey) {
                throw new Error('API密钥未配置');
            }

            const model = window.MDAC_AI_CONFIG?.GEMINI_CONFIG?.DEFAULT_MODEL ||
                         GEMINI_CONFIG?.DEFAULT_MODEL ||
                         'gemini-2.5-flash-lite-preview-06-17';

            const url = `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${apiKey}`;

            const requestBody = {
                contents: [{
                    parts: [{
                        text: context ? `${context}\n\n${prompt}` : prompt
                    }]
                }],
                generationConfig: window.MDAC_AI_CONFIG?.GEMINI_CONFIG?.DEFAULT_GENERATION_CONFIG ||
                                 GEMINI_CONFIG?.DEFAULT_GENERATION_CONFIG,
                safetySettings: window.MDAC_AI_CONFIG?.GEMINI_CONFIG?.SAFETY_SETTINGS ||
                               GEMINI_CONFIG?.SAFETY_SETTINGS
            };

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(`API请求失败: ${response.status} ${response.statusText}. ${errorData.error?.message || ''}`);
            }

            const data = await response.json();
            
            if (!data.candidates || data.candidates.length === 0) {
                throw new Error('AI未返回有效响应');
            }

            const candidate = data.candidates[0];
            if (candidate.finishReason === 'SAFETY') {
                throw new Error('AI响应被安全过滤器阻止');
            }

            const content = candidate.content?.parts?.[0]?.text;
            if (!content) {
                throw new Error('AI响应内容为空');
            }

            return content.trim();

        } catch (error) {
            console.error('❌ Gemini API调用失败:', error);
            throw error;
        }
    }

    /**
     * 解析内容
     */
    async parseContent() {
        const contentInput = document.getElementById('contentInput');
        const content = contentInput?.value?.trim();

        if (!content) {
            this.showMessage('请先输入要解析的内容', 'warning');
            return;
        }

        this.showParsingStatus(true);
        this.aiStatus = 'working';
        this.updateAIStatus();

        try {
            const response = await chrome.runtime.sendMessage({
                action: 'callGeminiAI',
                prompt: `请从以下内容中提取MDAC表单所需的个人信息：\n\n${content}`,
                context: 'MDAC表单填充'
            });

            if (response && response.success) {
                this.parsedData = this.parseAIResponse(response.data);
                this.displayParseResults();
                this.showMessage('内容解析完成', 'success');
            } else {
                throw new Error(response?.error || 'AI解析失败');
            }
        } catch (error) {
            console.error('内容解析失败:', error);
            this.showMessage('内容解析失败: ' + error.message, 'error');
        }

        this.showParsingStatus(false);
        this.aiStatus = 'ready';
        this.updateAIStatus();
    }

    /**
     * 处理图片上传
     */
    triggerImageUpload() {
        document.getElementById('imageInput')?.click();
    }

    /**
     * 处理图片上传事件
     */
    async handleImageUpload(event) {
        const file = event.target.files[0];
        if (!file) return;

        if (!file.type.startsWith('image/')) {
            this.showMessage('请选择有效的图片文件', 'error');
            return;
        }

        this.showParsingStatus(true);
        this.aiStatus = 'working';
        this.updateAIStatus();

        try {
            const base64 = await this.fileToBase64(file);
            const response = await chrome.runtime.sendMessage({
                action: 'callGeminiVision',
                image: base64,
                prompt: '请提取图片中的文字信息，特别是个人身份信息、护照信息、联系方式等'
            });

            if (response && response.success) {
                // 将提取的文字填入内容输入框
                const contentInput = document.getElementById('contentInput');
                if (contentInput) {
                    contentInput.value = response.data;
                }
                this.showMessage('图片文字提取成功', 'success');
            } else {
                throw new Error(response?.error || '图片文字提取失败');
            }
        } catch (error) {
            console.error('图片处理失败:', error);
            this.showMessage('图片处理失败: ' + error.message, 'error');
        }

        this.showParsingStatus(false);
        this.aiStatus = 'ready';
        this.updateAIStatus();
    }

    /**
     * 文件转Base64
     */
    fileToBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => {
                const base64 = reader.result.split(',')[1];
                resolve(base64);
            };
            reader.onerror = reject;
            reader.readAsDataURL(file);
        });
    }

    /**
     * 处理补充信息输入
     */
    async handleSupplementInput() {
        const supplementInput = document.getElementById('supplementInput');
        const rawText = supplementInput?.value || '';

        // 解析补充信息
        const parsedSupplement = this.parseSupplementText(rawText);

        // 保存到本地存储
        this.supplementData = {
            rawText: rawText,
            parsedFields: parsedSupplement,
            timestamp: Date.now()
        };

        try {
            await chrome.storage.local.set({ 'mdac_supplement_data': this.supplementData });
            this.updateSupplementStatus();
        } catch (error) {
            console.error('保存补充信息失败:', error);
        }
    }

    /**
     * 解析补充信息文本
     */
    parseSupplementText(text) {
        const fields = {};
        const lines = text.split('\n');

        const fieldMappings = {
            '邮箱': 'email',
            '电话': 'mobileNo',
            '国家代码': 'countryCode',
            '航班号': 'flightNo',
            '到达日期': 'arrivalDate',
            '离开日期': 'departureDate',
            '住宿类型': 'accommodation',
            '地址': 'address',
            '州': 'state',
            '邮编': 'postcode',
            '城市': 'city',
            '旅行方式': 'modeOfTravel',
            '最后港口': 'lastPort'
        };

        lines.forEach(line => {
            const colonIndex = line.indexOf('：');
            if (colonIndex > 0) {
                const key = line.substring(0, colonIndex).trim();
                const value = line.substring(colonIndex + 1).trim();

                if (fieldMappings[key] && value) {
                    fields[fieldMappings[key]] = value;
                }
            }
        });

        return fields;
    }

    /**
     * 更新补充信息状态
     */
    updateSupplementStatus() {
        const statusElement = document.querySelector('#supplementStatus .status-text');
        if (statusElement && this.supplementData) {
            const fieldCount = Object.keys(this.supplementData.parsedFields || {}).length;
            const charCount = (this.supplementData.rawText || '').length;

            if (fieldCount > 0) {
                statusElement.textContent = `已保存${fieldCount}个字段，${charCount}个字符`;
            } else if (charCount > 0) {
                statusElement.textContent = `已保存${charCount}个字符，等待解析`;
            } else {
                statusElement.textContent = '暂无保存的补充信息';
            }
        }
    }

    /**
     * 清空补充信息
     */
    async clearSupplementData() {
        try {
            await chrome.storage.local.remove(['mdac_supplement_data']);
            this.supplementData = null;

            const supplementInput = document.getElementById('supplementInput');
            if (supplementInput) {
                supplementInput.value = '';
            }

            this.updateSupplementStatus();
            this.showMessage('补充信息已清空', 'success');
        } catch (error) {
            console.error('清空补充信息失败:', error);
            this.showMessage('清空失败', 'error');
        }
    }

    /**
     * 预览合并数据
     */
    previewMergedData() {
        if (!this.parsedData && !this.supplementData) {
            this.showMessage('暂无数据可预览', 'warning');
            return;
        }

        this.mergedData = this.mergeDataSources();

        if (this.dataPreviewManager) {
            this.dataPreviewManager.showMergedDataPreview(
                this.parsedData,
                this.supplementData?.parsedFields,
                this.mergedData
            );
        }
    }

    /**
     * 合并数据源
     */
    mergeDataSources() {
        const merged = {};

        // 先添加补充信息数据
        if (this.supplementData && this.supplementData.parsedFields) {
            Object.assign(merged, this.supplementData.parsedFields);
        }

        // AI解析数据优先级更高，会覆盖补充信息中的同名字段
        if (this.parsedData) {
            Object.assign(merged, this.parsedData);
        }

        return merged;
    }

    /**
     * 填充表单
     */
    async fillForm() {
        if (!this.isMDACPage) {
            this.showMessage('请先打开MDAC网站', 'warning');
            return;
        }

        // 如果有多个数据源，显示预览
        if (this.parsedData && this.supplementData) {
            this.previewMergedData();
            return;
        }

        // 单一数据源直接填充
        const dataToFill = this.parsedData || this.supplementData?.parsedFields;
        if (!dataToFill) {
            this.showMessage('暂无数据可填充', 'warning');
            return;
        }

        await this.performFormFill(dataToFill);
    }

    /**
     * 执行表单填充
     */
    async performFormFill(data) {
        let sessionId = null;

        try {
            // 准备字段列表用于进度可视化
            const fieldList = Object.entries(data).map(([key, value]) => ({
                key,
                label: this.getFieldLabel(key),
                value: value
            }));

            // 启动进度可视化
            if (this.progressVisualizer) {
                this.progressVisualizer.startProgress({
                    totalFields: fieldList.length,
                    fieldList: fieldList,
                    title: 'MDAC表单填充进度',
                    showInSidepanel: true
                });
            }

            // 启动填充监控会话
            if (this.fillMonitor) {
                sessionId = this.fillMonitor.startFillSession(
                    data,
                    {}, // 字段映射将在content script中获取
                    (progress) => this.updateFillProgress(progress)
                );
                console.log(`🚀 开始填充监控会话: ${sessionId}`);
            }

            // 发送填充请求到content script
            const response = await chrome.tabs.sendMessage(this.currentTab.id, {
                action: 'fillForm',
                data: data,
                sessionId: sessionId, // 传递会话ID
                fieldList: fieldList  // 传递字段列表用于进度更新
            });

            if (response && response.success) {
                this.showMessage('表单填充成功', 'success');

                // 结束填充会话
                if (this.fillMonitor && sessionId) {
                    this.fillMonitor.endFillSession('success');
                }

                // 触发首次填充成功引导
                if (this.userGuideManager) {
                    this.userGuideManager.triggerScenarioGuide('firstFillSuccess');
                }
            } else {
                throw new Error(response?.error || '表单填充失败');
            }
        } catch (error) {
            console.error('表单填充失败:', error);

            // 结束填充会话（失败状态）
            if (this.fillMonitor && sessionId) {
                this.fillMonitor.endFillSession('failed');
            }

            // 使用错误恢复管理器处理错误
            if (this.errorRecoveryManager) {
                this.errorRecoveryManager.handleError(error, {
                    operation: 'performFormFill',
                    data: data,
                    sessionId: sessionId
                });
            } else {
                this.showMessage('表单填充失败: ' + error.message, 'error');
            }
        }
    }

    /**
     * 更新填充进度
     * @param {Object} progress 进度信息
     */
    updateFillProgress(progress) {
        console.log('📊 填充进度更新:', progress);

        // 更新进度显示
        const progressElement = document.getElementById('fillProgress');
        if (progressElement) {
            progressElement.style.display = 'block';
            progressElement.innerHTML = `
                <div class="progress-header">
                    <span class="progress-title">表单填充进度</span>
                    <span class="progress-percentage">${progress.percentage}%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${progress.percentage}%"></div>
                </div>
                <div class="progress-details">
                    <span class="progress-stat">✅ 成功: ${progress.successful}</span>
                    <span class="progress-stat">❌ 失败: ${progress.failed}</span>
                    <span class="progress-stat">⏭️ 跳过: ${progress.skipped}</span>
                    <span class="progress-stat">⏳ 进行中: ${progress.inProgress}</span>
                </div>
            `;
        }

        // 如果填充完成，隐藏进度条
        if (progress.percentage === 100) {
            setTimeout(() => {
                if (progressElement) {
                    progressElement.style.display = 'none';
                }
            }, 3000);
        }
    }

    /**
     * 更新字段填充状态（供content script调用）
     * @param {string} fieldKey 字段键
     * @param {string} status 状态
     * @param {Object} details 详细信息
     */
    updateFieldStatus(fieldKey, status, details = {}) {
        // 更新进度可视化器
        if (this.progressVisualizer) {
            this.progressVisualizer.updateFieldStatus(fieldKey, status, details);
        }

        console.log(`📝 字段状态更新: ${fieldKey} -> ${status}`);
    }

    /**
     * 一键智能填充
     */
    async quickFill() {
        if (!this.isMDACPage) {
            this.showMessage('请先打开MDAC网站', 'warning');
            return;
        }

        // 合并所有可用数据
        const mergedData = this.mergeDataSources();
        if (Object.keys(mergedData).length === 0) {
            this.showMessage('暂无数据可填充', 'warning');
            return;
        }

        await this.performFormFill(mergedData);
    }

    /**
     * 显示消息
     */
    showMessage(message, type = 'info') {
        // 创建消息提示
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;
        messageDiv.textContent = message;
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 16px;
            border-radius: 4px;
            color: white;
            font-size: 13px;
            z-index: 10000;
            max-width: 300px;
            word-wrap: break-word;
        `;

        // 设置背景色
        switch (type) {
            case 'success':
                messageDiv.style.background = '#4caf50';
                break;
            case 'error':
                messageDiv.style.background = '#f44336';
                break;
            case 'warning':
                messageDiv.style.background = '#ff9800';
                break;
            default:
                messageDiv.style.background = '#2196f3';
        }

        document.body.appendChild(messageDiv);

        // 3秒后自动移除
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 3000);
    }

    /**
     * 显示解析状态
     */
    showParsingStatus(show) {
        const statusElement = document.getElementById('parsingStatus');
        if (statusElement) {
            statusElement.style.display = show ? 'block' : 'none';
        }
    }

    /**
     * 清空内容
     */
    clearContent() {
        const contentInput = document.getElementById('contentInput');
        if (contentInput) {
            contentInput.value = '';
        }

        this.parsedData = null;
        this.hideParseResults();
        this.showMessage('内容已清空', 'success');
    }

    /**
     * 隐藏解析结果
     */
    hideParseResults() {
        const resultsElement = document.getElementById('parseResults');
        if (resultsElement) {
            resultsElement.style.display = 'none';
        }
    }

    /**
     * 解析AI响应
     */
    parseAIResponse(response) {
        // 这里应该包含解析AI响应的逻辑
        // 简化版本，实际应该更复杂
        try {
            if (typeof response === 'string') {
                // 尝试从文本中提取结构化数据
                return this.extractStructuredData(response);
            }
            return response;
        } catch (error) {
            console.error('解析AI响应失败:', error);
            return {};
        }
    }

    /**
     * 从文本中提取结构化数据
     */
    extractStructuredData(text) {
        const data = {};
        // 简化的提取逻辑
        // 实际应该更复杂和准确
        return data;
    }

    /**
     * 显示解析结果
     */
    displayParseResults() {
        const resultsElement = document.getElementById('parseResults');
        if (resultsElement && this.parsedData) {
            resultsElement.style.display = 'block';

            // 进行置信度评估
            if (this.confidenceEvaluator) {
                const evaluation = this.confidenceEvaluator.evaluateConfidence(this.parsedData);
                this.displayConfidenceEvaluation(evaluation);
            }

            // 显示解析结果的详细内容
            this.renderParseResultsContent();
        }
    }

    /**
     * 显示置信度评估结果
     */
    displayConfidenceEvaluation(evaluation) {
        const resultsElement = document.getElementById('parseResults');
        if (!resultsElement) return;

        const visualization = this.confidenceEvaluator.createConfidenceVisualization(evaluation);

        // 创建置信度评估显示区域
        let confidenceSection = resultsElement.querySelector('.confidence-section');
        if (!confidenceSection) {
            confidenceSection = document.createElement('div');
            confidenceSection.className = 'confidence-section';
            resultsElement.insertBefore(confidenceSection, resultsElement.firstChild);
        }

        confidenceSection.innerHTML = `
            <div class="confidence-header">
                <h3>
                    <span class="confidence-icon">${visualization.overall.icon}</span>
                    AI解析置信度评估
                </h3>
                <div class="overall-confidence">
                    <span class="confidence-score" style="color: ${visualization.overall.color}">
                        ${evaluation.overall.confidence}%
                    </span>
                    <span class="confidence-level">${visualization.overall.description}</span>
                </div>
            </div>

            <div class="confidence-progress">
                <div class="progress-bar-container">
                    <div class="progress-bar" style="width: ${evaluation.overall.confidence}%; background: ${visualization.overall.color}"></div>
                </div>
            </div>

            <div class="confidence-summary">
                <div class="summary-stats">
                    <div class="stat-item high">
                        <span class="stat-count">${evaluation.summary.highConfidence}</span>
                        <span class="stat-label">高置信度</span>
                    </div>
                    <div class="stat-item medium">
                        <span class="stat-count">${evaluation.summary.mediumConfidence}</span>
                        <span class="stat-label">中等置信度</span>
                    </div>
                    <div class="stat-item low">
                        <span class="stat-count">${evaluation.summary.lowConfidence}</span>
                        <span class="stat-label">低置信度</span>
                    </div>
                    <div class="stat-item critical">
                        <span class="stat-count">${evaluation.summary.criticalConfidence}</span>
                        <span class="stat-label">极低置信度</span>
                    </div>
                </div>
            </div>

            ${evaluation.overall.issues.length > 0 ? `
                <div class="confidence-issues">
                    <h4>⚠️ 发现的问题</h4>
                    <ul class="issues-list">
                        ${evaluation.overall.issues.map(issue => `<li>${issue}</li>`).join('')}
                    </ul>
                </div>
            ` : ''}

            ${evaluation.overall.recommendations.length > 0 ? `
                <div class="confidence-recommendations">
                    <h4>💡 建议</h4>
                    <ul class="recommendations-list">
                        ${evaluation.overall.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                    </ul>
                </div>
            ` : ''}

            <div class="confidence-actions">
                <button class="confidence-btn secondary" onclick="window.mdacSidepanel.showDetailedConfidence('${evaluation.evaluationId}')">
                    查看详细分析
                </button>
                <button class="confidence-btn primary" onclick="window.mdacSidepanel.proceedWithConfidence()">
                    继续使用数据
                </button>
            </div>
        `;

        // 暴露到全局以供按钮调用
        window.mdacSidepanel = this;

        // 存储当前评估结果
        this.currentEvaluation = evaluation;

        console.log('📊 置信度评估结果已显示');
    }

    /**
     * 渲染解析结果内容
     */
    renderParseResultsContent() {
        const resultsElement = document.getElementById('parseResults');
        if (!resultsElement || !this.parsedData) return;

        // 创建或更新数据显示区域
        let dataSection = resultsElement.querySelector('.data-section');
        if (!dataSection) {
            dataSection = document.createElement('div');
            dataSection.className = 'data-section';
            resultsElement.appendChild(dataSection);
        }

        const fieldCount = Object.keys(this.parsedData).length;

        dataSection.innerHTML = `
            <div class="data-header">
                <h3>解析结果 (${fieldCount}个字段)</h3>
                <div class="data-actions">
                    <button class="data-btn" onclick="window.mdacSidepanel.editParsedData()">编辑</button>
                    <button class="data-btn" onclick="window.mdacSidepanel.exportData()">导出</button>
                </div>
            </div>
            <div class="data-fields">
                ${this.renderDataFields()}
            </div>
        `;
    }

    /**
     * 渲染数据字段
     */
    renderDataFields() {
        if (!this.parsedData) return '';

        return Object.entries(this.parsedData).map(([key, value]) => {
            const fieldEval = this.currentEvaluation?.fields[key];
            const confidenceClass = fieldEval ? `confidence-${fieldEval.level}` : '';
            const confidenceIcon = fieldEval ? fieldEval.icon : '';
            const confidenceScore = fieldEval ? `${fieldEval.confidence}%` : '';

            return `
                <div class="data-field ${confidenceClass}">
                    <div class="field-header">
                        <span class="field-label">${this.getFieldLabel(key)}</span>
                        <div class="field-confidence">
                            <span class="confidence-icon">${confidenceIcon}</span>
                            <span class="confidence-score">${confidenceScore}</span>
                        </div>
                    </div>
                    <div class="field-value">${value || '<span class="empty-value">未填写</span>'}</div>
                    ${fieldEval && fieldEval.issues.length > 0 ? `
                        <div class="field-issues">
                            ${fieldEval.issues.map(issue => `<span class="issue-tag">${issue}</span>`).join('')}
                        </div>
                    ` : ''}
                </div>
            `;
        }).join('');
    }

    /**
     * 获取字段标签
     */
    getFieldLabel(fieldKey) {
        const labels = {
            name: '姓名',
            passportNo: '护照号码',
            dateOfBirth: '出生日期',
            nationality: '国籍',
            sex: '性别',
            passportExpiry: '护照到期日',
            email: '电子邮箱',
            confirmEmail: '确认邮箱',
            countryCode: '国家代码',
            mobileNo: '手机号码',
            arrivalDate: '到达日期',
            departureDate: '离开日期',
            flightNo: '航班号',
            modeOfTravel: '旅行方式',
            lastPort: '最后港口',
            accommodation: '住宿类型',
            address: '地址',
            address2: '地址2',
            state: '州/省',
            postcode: '邮政编码',
            city: '城市'
        };
        return labels[fieldKey] || fieldKey;
    }

    /**
     * 显示详细置信度分析
     */
    showDetailedConfidence(evaluationId) {
        if (!this.currentEvaluation) return;

        const modal = document.createElement('div');
        modal.className = 'confidence-detail-modal';
        modal.innerHTML = `
            <div class="modal-overlay" onclick="this.parentElement.remove()"></div>
            <div class="modal-container">
                <div class="modal-header">
                    <h3>详细置信度分析</h3>
                    <button class="modal-close" onclick="this.parentElement.parentElement.remove()">×</button>
                </div>
                <div class="modal-content">
                    <div class="detailed-fields">
                        ${Object.entries(this.currentEvaluation.fields).map(([fieldKey, fieldEval]) => `
                            <div class="detailed-field">
                                <div class="field-header">
                                    <span class="field-name">${this.getFieldLabel(fieldKey)}</span>
                                    <span class="field-confidence confidence-${fieldEval.level}">
                                        ${fieldEval.icon} ${fieldEval.confidence}%
                                    </span>
                                </div>
                                <div class="field-value">${fieldEval.value}</div>
                                <div class="field-details">
                                    <div class="detail-scores">
                                        <div class="score-item">
                                            <span class="score-label">格式</span>
                                            <span class="score-value">${fieldEval.details.formatScore}%</span>
                                        </div>
                                        <div class="score-item">
                                            <span class="score-label">长度</span>
                                            <span class="score-value">${fieldEval.details.lengthScore}%</span>
                                        </div>
                                        <div class="score-item">
                                            <span class="score-label">模式</span>
                                            <span class="score-value">${fieldEval.details.patternScore}%</span>
                                        </div>
                                        <div class="score-item">
                                            <span class="score-label">值</span>
                                            <span class="score-value">${fieldEval.details.valueScore}%</span>
                                        </div>
                                        <div class="score-item">
                                            <span class="score-label">上下文</span>
                                            <span class="score-value">${fieldEval.details.contextScore}%</span>
                                        </div>
                                    </div>
                                    ${fieldEval.issues.length > 0 ? `
                                        <div class="field-issues">
                                            <h5>问题:</h5>
                                            <ul>${fieldEval.issues.map(issue => `<li>${issue}</li>`).join('')}</ul>
                                        </div>
                                    ` : ''}
                                    ${fieldEval.recommendations.length > 0 ? `
                                        <div class="field-recommendations">
                                            <h5>建议:</h5>
                                            <ul>${fieldEval.recommendations.map(rec => `<li>${rec}</li>`).join('')}</ul>
                                        </div>
                                    ` : ''}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    /**
     * 继续使用数据（忽略置信度警告）
     */
    proceedWithConfidence() {
        if (this.currentEvaluation && this.currentEvaluation.overall.confidence < 50) {
            if (!confirm('当前数据置信度较低，确定要继续使用吗？建议先检查和修正数据。')) {
                return;
            }
        }

        this.showMessage('已确认使用AI解析数据', 'success');

        // 触发数据预览或直接填充
        if (this.dataPreviewManager) {
            this.showDataPreview();
        }
    }

    /**
     * 编辑解析数据
     */
    editParsedData() {
        // 这里可以打开数据编辑界面
        if (this.dataPreviewManager) {
            this.showDataPreview();
        } else {
            this.showMessage('数据预览功能未初始化', 'warning');
        }
    }

    /**
     * 导出数据
     */
    exportData() {
        if (!this.parsedData) {
            this.showMessage('没有可导出的数据', 'warning');
            return;
        }

        const dataText = JSON.stringify(this.parsedData, null, 2);
        const blob = new Blob([dataText], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `mdac-data-${Date.now()}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showMessage('数据已导出', 'success');
    }

    /**
     * 打开表单编辑器
     */
    openFormEditor() {
        chrome.tabs.create({ url: 'src/ui/form-editor/form-editor.html' });
    }

    /**
     * 打开AI设置
     */
    openAISettings() {
        chrome.runtime.openOptionsPage();
    }

    /**
     * 显示帮助
     */
    showHelp() {
        this.showModal('帮助信息', '这里是MDAC AI助手的使用说明...');
    }

    /**
     * 显示模态框
     */
    showModal(title, content) {
        const modal = document.getElementById('modal');
        const modalTitle = document.getElementById('modalTitle');
        const modalBody = document.getElementById('modalBody');

        if (modal && modalTitle && modalBody) {
            modalTitle.textContent = title;
            modalBody.innerHTML = content;
            modal.classList.add('show');
        }
    }

    /**
     * 关闭模态框
     */
    closeModal() {
        const modal = document.getElementById('modal');
        if (modal) {
            modal.classList.remove('show');
        }
    }

    /**
     * 处理模态框确认
     */
    handleModalConfirm() {
        // 处理确认操作
        this.closeModal();
    }

    // ===== 新增的分离解析功能 =====

    /**
     * 解析个人信息
     */
    async parsePersonalInfo() {
        console.log('🔍 parsePersonalInfo 方法被调用');
        console.log('🔍 this 对象:', this);
        console.log('🔍 this.callGeminiAPI:', typeof this.callGeminiAPI);
        
        const input = document.getElementById('personalInfoInput');
        if (!input || !input.value.trim()) {
            this.showMessage('请输入个人信息内容', 'warning');
            return;
        }

        try {
            this.showMessage('正在解析个人信息...', 'info');

            // 检查 callGeminiAPI 方法是否存在
            if (typeof this.callGeminiAPI !== 'function') {
                throw new Error('callGeminiAPI 方法不存在或不是函数');
            }

            // 使用专门的个人信息解析提示词
            const prompt = window.MDAC_AI_CONFIG?.AI_PROMPTS?.PERSONAL_INFO_PARSING ||
                          (typeof AI_PROMPTS !== 'undefined' ? AI_PROMPTS?.PERSONAL_INFO_PARSING : null);

            if (!prompt) {
                throw new Error('个人信息解析提示词未找到');
            }

            const result = await this.callGeminiAPI(
                prompt.replace('{content}', input.value.trim()),
                window.MDAC_AI_CONFIG?.AI_CONTEXTS?.PERSONAL_INFO_EXTRACTOR ||
                (typeof AI_CONTEXTS !== 'undefined' ? AI_CONTEXTS?.PERSONAL_INFO_EXTRACTOR : '')
            );

            if (result) {
                const personalData = this.parseAIResponse(result);
                this.fillPersonalFields(personalData);
                this.showMessage('个人信息解析完成！', 'success');

                // 清空输入框
                input.value = '';
            }
        } catch (error) {
            console.error('个人信息解析失败:', error);
            this.showMessage('个人信息解析失败: ' + error.message, 'error');
        }
    }

    /**
     * 解析旅行信息
     */
    async parseTravelInfo() {
        const input = document.getElementById('travelInfoInput');
        if (!input || !input.value.trim()) {
            this.showMessage('请输入旅行信息内容', 'warning');
            return;
        }

        try {
            this.showMessage('正在解析旅行信息...', 'info');

            // 使用专门的旅行信息解析提示词
            const prompt = window.MDAC_AI_CONFIG?.AI_PROMPTS?.TRAVEL_INFO_PARSING ||
                          AI_PROMPTS?.TRAVEL_INFO_PARSING;

            if (!prompt) {
                throw new Error('旅行信息解析提示词未找到');
            }

            const result = await this.callGeminiAPI(
                prompt.replace('{content}', input.value.trim()),
                window.MDAC_AI_CONFIG?.AI_CONTEXTS?.TRAVEL_INFO_EXTRACTOR ||
                AI_CONTEXTS?.TRAVEL_INFO_EXTRACTOR
            );

            if (result) {
                const travelData = this.parseAIResponse(result);
                this.fillTravelFields(travelData);
                this.showMessage('旅行信息解析完成！', 'success');

                // 清空输入框
                input.value = '';
            }
        } catch (error) {
            console.error('旅行信息解析失败:', error);
            this.showMessage('旅行信息解析失败: ' + error.message, 'error');
        }
    }

    /**
     * 填充个人信息字段
     */
    fillPersonalFields(data) {
        if (!data) return;

        const personalFields = {
            'name': data.name,
            'passportNo': data.passportNo,
            'dateOfBirth': data.dateOfBirth,
            'nationality': data.nationality,
            'sex': data.sex,
            'passportExpiry': data.passportExpiry,
            'presetEmail': data.email || data.confirmEmail,
            'presetPhone': data.mobileNo ? (data.countryCode || '') + data.mobileNo : null
        };

        Object.entries(personalFields).forEach(([fieldId, value]) => {
            if (value !== null && value !== undefined) {
                const field = document.getElementById(fieldId);
                if (field) {
                    field.value = value;
                    field.classList.add('filled');

                    // 添加成功状态指示
                    const statusElement = field.parentElement.querySelector('.field-status');
                    if (statusElement) {
                        statusElement.className = 'field-status success';
                    }
                }
            }
        });
    }

    /**
     * 填充旅行信息字段
     */
    fillTravelFields(data) {
        if (!data) return;

        const travelFields = {
            'arrivalDate': data.arrivalDate,
            'departureDate': data.departureDate,
            'flightNo': data.flightNo,
            'accommodation': data.accommodation,
            'address': data.address,
            'city': data.city
        };

        Object.entries(travelFields).forEach(([fieldId, value]) => {
            if (value !== null && value !== undefined) {
                const field = document.getElementById(fieldId);
                if (field) {
                    field.value = value;
                    field.classList.add('filled');

                    // 添加成功状态指示
                    const statusElement = field.parentElement.querySelector('.field-status');
                    if (statusElement) {
                        statusElement.className = 'field-status success';
                    }
                }
            }
        });
    }

    /**
     * 清除所有数据
     */
    clearAllData() {
        // 清除输入框
        const inputs = ['personalInfoInput', 'travelInfoInput', 'presetEmail', 'presetPhone'];
        inputs.forEach(id => {
            const element = document.getElementById(id);
            if (element) element.value = '';
        });

        // 清除所有字段
        const allFields = document.querySelectorAll('.field-input, .preset-input');
        allFields.forEach(field => {
            field.value = '';
            field.classList.remove('filled');
        });

        // 清除状态指示器
        const statusElements = document.querySelectorAll('.field-status');
        statusElements.forEach(status => {
            status.className = 'field-status';
        });

        this.showMessage('所有数据已清除', 'info');
    }

    /**
     * 预览所有数据
     */
    previewAllData() {
        const allData = this.collectAllFormData();
        const preview = this.formatDataPreview(allData);
        this.showModal('数据预览', preview);
    }

    /**
     * 收集所有表单数据
     */
    collectAllFormData() {
        const data = {};

        // 收集所有字段数据
        const fields = document.querySelectorAll('.field-input, .preset-input');
        fields.forEach(field => {
            if (field.value) {
                data[field.id] = field.value;
            }
        });

        return data;
    }

    /**
     * 格式化数据预览
     */
    formatDataPreview(data) {
        let html = '<div class="data-preview">';

        if (Object.keys(data).length === 0) {
            html += '<p>暂无数据</p>';
        } else {
            html += '<table class="preview-table">';
            Object.entries(data).forEach(([key, value]) => {
                html += `<tr><td><strong>${key}:</strong></td><td>${value}</td></tr>`;
            });
            html += '</table>';
        }

        html += '</div>';
        return html;
    }

    /**
     * 更新到MDAC页面
     */
    async updateToMDAC() {
        if (!this.isMDACPage) {
            this.showMessage('请先打开MDAC网站', 'warning');
            return;
        }

        try {
            const data = this.collectAllFormData();
            if (Object.keys(data).length === 0) {
                this.showMessage('没有数据可以更新', 'warning');
                return;
            }

            this.showMessage('正在更新到MDAC页面...', 'info');

            // 这里调用现有的填充方法
            await this.fillForm();

        } catch (error) {
            console.error('更新到MDAC失败:', error);
            this.showMessage('更新失败: ' + error.message, 'error');
        }
    }

    /**
     * 编辑预设信息
     */
    editPresetInfo() {
        const emailField = document.getElementById('presetEmail');
        const phoneField = document.getElementById('presetPhone');

        if (emailField) emailField.focus();

        this.showMessage('请编辑常用邮箱和电话信息', 'info');
    }

    /**
     * 保存数据
     */
    async saveData() {
        try {
            const data = this.collectAllFormData();

            // 保存到Chrome存储
            await chrome.storage.local.set({
                'mdac_saved_data': data,
                'mdac_save_timestamp': Date.now()
            });

            this.showMessage('数据已保存', 'success');
        } catch (error) {
            console.error('保存数据失败:', error);
            this.showMessage('保存失败: ' + error.message, 'error');
        }
    }

    /**
     * 显示设置
     */
    showSettings() {
        this.showModal('设置', '<p>设置功能开发中...</p>');
    }
}

// 初始化侧边栏
const mdacSidePanel = new MDACAssistantSidePanel();
